import React from 'react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import type { AnalyticEvent, ShiftReportEventName } from 'app/analytics/eventsTypes';
import { useNavigate } from 'react-router';
import { DateContent, DateTableCell } from '../../components/TableCells/DateTableCell';
import { TextContent, TextTableCell } from '../../components/TableCells/TextTableCell';
import { UserContent, UserTableCell } from '../../components/TableCells/UserTableCell';

export type ShiftReportAnalyticEvent = AnalyticEvent<ShiftReportEventName>;

type InReviewShiftReportsTableRowProps = {
  rowData: ShiftReportBasicDetailsSchema;
  project: ProjectSchema;
  isLargeScreen: boolean;
};

export const InReviewShiftReportsTableRow: React.FC<InReviewShiftReportsTableRowProps> = ({
  rowData,
  project,
  isLargeScreen,
}) => {
  const navigate = useNavigate();

  const handleRowClick = (event: React.SyntheticEvent) => {
    event.preventDefault();
    navigate(`/projects/${project.id}/shift-reports/${rowData.id}`, {
      state: { tab: 'review' },
    });
  };

  if (isLargeScreen) {
    return (
      <Table.Row key={rowData.id} onClick={handleRowClick}>
        <DateTableCell date={rowData.reportDate} />
        <TextTableCell text={rowData.shiftType} />
        <UserTableCell projectId={project.id} teamMemberId={rowData.approverId ?? undefined} />
        <UserTableCell projectId={project.id} teamMemberId={rowData.teamMemberId} />
        <TextTableCell text={rowData.contractorName} />
      </Table.Row>
    );
  }

  return (
    <Table.Row key={rowData.id} onClick={handleRowClick}>
      <Table.Cell className="flex justify-between items-start">
        <div className="flex grow flex-col gap-3">
          <div className="flex gap-6">
            <DateContent date={rowData.reportDate} />
            <TextContent text={rowData.shiftType} />
          </div>
          <UserContent projectId={project.id} teamMemberId={rowData.teamMemberId} />
          <TextContent text={rowData.contractorName} />
        </div>
      </Table.Cell>
    </Table.Row>
  );
};
