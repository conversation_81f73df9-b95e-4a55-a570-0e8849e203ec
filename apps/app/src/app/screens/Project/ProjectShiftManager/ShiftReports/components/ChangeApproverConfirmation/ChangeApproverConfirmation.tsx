import React, { type ComponentProps } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import ConfirmationModal from '@shape-construction/arch-ui/src/ConfirmationModal';
import IconBadge from '@shape-construction/arch-ui/src/IconBadge';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';

type ChangeApproverConfirmationProps = ComponentProps<typeof ConfirmationModal.Root> & {
  onConfirm: () => void;
};

export const ChangeApproverConfirmation: React.FC<ChangeApproverConfirmationProps> = ({ onConfirm, ...props }) => {
  const messages = useMessageGetter('shiftReport.changeApprovalConfirmation');

  return (
    <ConfirmationModal.Root {...props}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="warning">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          <div>{messages('subTitle')}</div>
          <ul className="list-disc pl-5">
            <li>{messages('firstPoint')}</li>
            <li>{messages('secondPoint')}</li>
          </ul>
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={() => props.onClose()}>
          {messages('cancel')}
        </Button>
        <Button color="warning" variant="contained" size="md" onClick={onConfirm}>
          {messages('confirm')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
