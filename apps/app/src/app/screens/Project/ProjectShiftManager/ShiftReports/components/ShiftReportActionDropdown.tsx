import React, { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import Divider from '@shape-construction/arch-ui/src/Divider';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import {
  ChevronRightIcon,
  DocumentTextIcon,
  EllipsisVerticalIcon,
  LockClosedIcon,
  UserGroupIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import Switch from '@shape-construction/arch-ui/src/Switch';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { useShiftReportDetails } from 'app/hooks/useShiftReportInputsDisabled';
import { Controller } from 'react-hook-form';
import { useShiftReportFormContext } from '../ShiftReportForm/ShiftReportForm';
import { ApproverSelect } from './ApproverSelect/ApproverSelect';
import { OverrideWarningModal } from './OverrideWarningModal';
import { ShiftReportCollaboratorsModal, type Step } from './ShiftReportCollaborators/ShiftReportCollaboratorsModal';

interface ShiftReportActionDropdownProps {
  previousShiftReport?: ShiftReportSchema;
  onOverride: () => void;
}

export const ShiftReportActionDropdown = ({ previousShiftReport, onOverride }: ShiftReportActionDropdownProps) => {
  const { value: shiftReportApprovalsFlag } = useFeatureFlag('shift-report-approvals');
  const messages = useMessageGetter('shiftReport.new');
  const visibilityMessages = useMessageGetter('shiftReport.form.visibility');
  const collaboratorsMessages = useMessageGetter('shiftReport.collaborators');
  const approvalMessages = useMessageGetter('shiftReport.approvals');

  const { data: shiftReport } = useShiftReportDetails();
  const { submitForm, control, watch } = useShiftReportFormContext();

  const visibility = watch('visibility', shiftReport?.visibility);
  const collaboratorsTeamMemberIds = watch('collaborators_team_member_ids', shiftReport?.collaboratorsTeamMemberIds);
  const canEditRootFields = shiftReport?.availableActions.editRootFields;

  const [stepCollaboratorsModal, setStepCollaboratorsModal] = useState<Step>(
    collaboratorsTeamMemberIds?.length ? 'list' : 'add'
  );
  const [openCollaboratorsModal, setOpenCollaboratorsModal] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const enableOverride = Boolean(previousShiftReport);

  const report = useMemo(() => {
    if (!previousShiftReport) return '';
    return `${parseDateWithFormat(previousShiftReport.reportDate, 'DD-MMM-YYYY')} ${previousShiftReport.shiftType}`;
  }, [previousShiftReport]);

  const handleCopy = () => {
    setOpenModal(true);
  };

  const handleSubmitModal = () => {
    onOverride();
    handleCloseModal();
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleOpenCollaboratorsModal = (open: boolean) => {
    setOpenCollaboratorsModal(open);
    setStepCollaboratorsModal(collaboratorsTeamMemberIds?.length ? 'list' : 'add');
  };

  return (
    <div>
      <Dropdown.Root>
        <Dropdown.Trigger asChild>
          <IconButton
            size="md"
            color="secondary"
            variant="text"
            icon={EllipsisVerticalIcon}
            aria-label="shift-report-actions"
          />
        </Dropdown.Trigger>

        <Dropdown.Items>
          <div className="pb-5">
            <Dropdown.Item icon={LockClosedIcon}>
              <div className="flex justify-between items-center">
                {visibilityMessages(visibility)}
                <Controller
                  name="visibility"
                  control={control}
                  render={({ field }) => (
                    <Switch.Root
                      checked={field.value === 'public'}
                      onCheckedChange={(checked: boolean) => {
                        field.onChange(checked ? 'public' : 'private');
                        submitForm();
                      }}
                      onClick={(e: React.SyntheticEvent) => {
                        e.stopPropagation();
                      }}
                    >
                      <Switch.Track>
                        <Switch.Thumb />
                      </Switch.Track>
                      <Switch.Label className="sr-only">visibility</Switch.Label>
                    </Switch.Root>
                  )}
                />
              </div>
            </Dropdown.Item>

            <Dropdown.Item onClick={handleCopy} icon={DocumentTextIcon} disabled={!enableOverride}>
              {messages('prefillFromPrevious')}
            </Dropdown.Item>

            <Dropdown.Item icon={UserGroupIcon} onClick={() => handleOpenCollaboratorsModal(true)}>
              <div className="flex justify-between items-center">
                {collaboratorsMessages(collaboratorsTeamMemberIds?.length ? 'editCollaborators' : 'addCollaborators')}
                <ChevronRightIcon className="w-4 h-4 text-gray-700" />
              </div>
            </Dropdown.Item>

            <Divider orientation="horizontal" />

            {shiftReportApprovalsFlag && canEditRootFields && (
              <Dropdown.Item onSelect={(e) => e.preventDefault()}>
                <div className="flex flex-col gap-1">
                  <span className="text-sm leading-5 font-medium text-neutral-bold">{approvalMessages('title')}</span>
                  <Controller
                    control={control}
                    name="approver_id"
                    render={({ field }) => (
                      <div className="w-full flex items-center gap-0.5">
                        <ApproverSelect field={field} onChange={submitForm} shiftReport={shiftReport} />
                      </div>
                    )}
                  />
                </div>
              </Dropdown.Item>
            )}
          </div>
        </Dropdown.Items>
      </Dropdown.Root>

      <OverrideWarningModal report={report} open={openModal} onClose={handleCloseModal} onSubmit={handleSubmitModal} />
      {openCollaboratorsModal && (
        <Controller
          name="collaborators_team_member_ids"
          control={control}
          render={({ field }) => (
            <ShiftReportCollaboratorsModal
              teamId={shiftReport?.teamId}
              step={stepCollaboratorsModal}
              setStep={setStepCollaboratorsModal}
              openModal={openCollaboratorsModal}
              setOpenModal={handleOpenCollaboratorsModal}
              value={collaboratorsTeamMemberIds || []}
              onSubmit={(collaborators) => {
                field.onChange(collaborators);
                submitForm();
              }}
            />
          )}
        />
      )}
    </div>
  );
};
