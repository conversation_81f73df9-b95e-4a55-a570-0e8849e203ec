import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import EmptyState from '@shape-construction/arch-ui/src/EmptyState';
import { NotAuthorisedIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import { ArrowUturnLeftIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useNavigate } from 'react-router';

export const NotAuthorizedReport: React.FC = () => {
  const messages = useMessageGetter('shiftReport.notAuthorizedReport');
  const project = useCurrentProject();
  const navigate = useNavigate();

  return (
    <div className="flex justify-center items-center flex-1 h-full bg-gray-100">
      <div className="max-w-sm">
        <EmptyState icon={<NotAuthorisedIcon />} title={messages('title')} body={messages('body')}>
          <EmptyState.PrimaryAction
            onClick={() => navigate(`/projects/${project.id}/shift-reports`)}
            leadingIcon={ArrowUturnLeftIcon}
          >
            {messages('goBack')}
          </EmptyState.PrimaryAction>
        </EmptyState>
      </div>
    </div>
  );
};
