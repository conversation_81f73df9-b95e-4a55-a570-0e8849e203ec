import React, { useMemo } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import {
  type ShiftReportSchema,
  type ShiftReportVisibilitySchema,
  shiftReportVisibilityEnum,
} from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import Button from '@shape-construction/arch-ui/src/Button';
import { DocumentTextIcon, QuestionMarkCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useMediaQuery, useModal } from '@shape-construction/hooks';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { useQuery } from '@tanstack/react-query';
import type { AnalyticEvent, ShiftReportEventName } from 'app/analytics/eventsTypes';
import { useShiftReportDetails } from 'app/hooks/useShiftReportInputsDisabled';
import { useSendAnalyticsEvent } from 'app/queries/analytics/analytics';
import { getProjectContributorsQueryOptions } from 'app/queries/projects/people';
import { useUpdateShiftReport } from 'app/queries/shiftReports/shiftReports';
import { Controller } from 'react-hook-form';
import { useParams } from 'react-router';
import { makeFormValuesFromReportData, useShiftReportFormContext } from '../ShiftReportForm/ShiftReportForm';
import { ApproverSelect } from './ApproverSelect/ApproverSelect';
import { OverrideWarningModal } from './OverrideWarningModal';
import { ShiftReportCollaborators } from './ShiftReportCollaborators';
import { VisibilitySelect } from './VisibilitySelect';

type Option = {
  key: ShiftReportVisibilitySchema;
  name: string;
};

type Params = {
  projectId: string;
  shiftReportId: string;
};

export type ShiftReportAnalyticEvent = AnalyticEvent<ShiftReportEventName>;

interface ShiftReportActionBarProps {
  previousShiftReport?: ShiftReportSchema;
  onOverride: () => void;
  isDraftSaving: boolean;
}

export const ShiftReportActionBar = ({ previousShiftReport, onOverride, isDraftSaving }: ShiftReportActionBarProps) => {
  const { value: shiftReportApprovalsFlag } = useFeatureFlag('shift-report-approvals');
  const { projectId, shiftReportId } = useParams() as Params;
  const { mutate: updateShiftReport } = useUpdateShiftReport();
  const { mutate: sendAnalyticsEvent } = useSendAnalyticsEvent();
  const messages = useMessageGetter('shiftReport.new');
  const visibilityMessages = useMessageGetter('shiftReport.visibility');
  const removedUserMessage = useMessage('shiftReport.list.table.removedUser');
  const approvalMessages = useMessageGetter('shiftReport.approvals');
  const { data: shiftReport } = useShiftReportDetails();
  const { data: people = [] } = useQuery(getProjectContributorsQueryOptions(projectId));
  const { submitForm, control, reset } = useShiftReportFormContext();

  const isMediumScreen = useMediaQuery(breakpoints.up('sm'));
  const canEditRootFields = shiftReport?.availableActions.editRootFields;

  const {
    open: isOverrideWarningModalOpen,
    openModal: openOverrideWarningModal,
    closeModal: closeOverrideWarningModal,
  } = useModal(false);

  const visibilityOptions: Option[] = useMemo(
    () => [
      { key: shiftReportVisibilityEnum.private, name: visibilityMessages('private') },
      { key: shiftReportVisibilityEnum.public, name: visibilityMessages('public') },
    ],
    [visibilityMessages]
  );
  const enableOverride = Boolean(previousShiftReport);

  const report = useMemo(() => {
    if (!previousShiftReport) return '';
    return `${parseDateWithFormat(previousShiftReport?.reportDate, 'DD-MMM-YYYY')} ${previousShiftReport?.shiftType}`;
  }, [previousShiftReport]);

  const isAuthor = shiftReport?.availableActions.editRootFields !== false;

  const author = useMemo(() => {
    const user = people.find((person) => person.id === shiftReport?.teamMemberId)?.user;
    if (!user) return undefined;
    return {
      id: user.id,
      name: user.name,
      firstName: user.firstName ?? undefined,
      lastName: user.lastName ?? undefined,
      avatarUrl: user.avatarUrl ?? undefined,
    };
  }, [people, shiftReport]);

  const handleSubmitModal = () => {
    onOverride();
    closeOverrideWarningModal();
    sendAnalyticsEvent({ event_name: 'copy_from_previous_shift_report' } as ShiftReportAnalyticEvent);
  };

  return (
    <>
      {isMediumScreen && (
        <div className="flex items-center">
          <div className="flex w-full items-center justify-between gap-6">
            <div className="flex items-center gap-4">
              {isAuthor ? (
                <div className="flex items-center gap-2">
                  <span className="hidden lg:block text-xs leading-4 font-medium text-neutral-subtlest">
                    {visibilityMessages('visibility')}:
                  </span>
                  <Controller
                    name="visibility"
                    control={control}
                    render={({ field }) => (
                      <VisibilitySelect {...field} ref={field.ref} value={field.value} options={visibilityOptions} />
                    )}
                  />
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="hidden text-gray-400 sm:block">{messages('author')}:</span>
                  <div className="flex items-center gap-1.5">
                    <UserAvatar size="sm" user={author} />
                    {author?.name ? (
                      <span className="text-sm text-gray-900 font-medium">{author?.name}</span>
                    ) : (
                      <span className="text-sm text-gray-500">{removedUserMessage}</span>
                    )}
                  </div>
                </div>
              )}
              {shiftReportApprovalsFlag && canEditRootFields && (
                <Controller
                  control={control}
                  name="approver_id"
                  render={({ field }) => (
                    <div className="flex items-center gap-2">
                      <p className="hidden md:block text-xs leading-4 font-medium text-neutral-subtlest">
                        {approvalMessages('title')}:
                      </p>
                      <ApproverSelect field={field} onChange={submitForm} shiftReport={shiftReport} />
                    </div>
                  )}
                />
              )}
              <Controller
                name="collaborators_team_member_ids"
                control={control}
                render={({ field }) => (
                  <ShiftReportCollaborators
                    value={field.value}
                    onChange={async (collaborators) => {
                      updateShiftReport(
                        {
                          projectId,
                          shiftReportId,
                          data: {
                            collaborators_team_member_ids: collaborators,
                          },
                        },
                        {
                          onSuccess: (updatedShiftReport) => reset(makeFormValuesFromReportData(updatedShiftReport)),
                        }
                      );
                    }}
                  />
                )}
              />
            </div>
            {isAuthor ? (
              <Button
                color="primary"
                variant="text"
                size="xs"
                aria-label={messages('prefillFromPrevious')}
                disabled={!enableOverride || isDraftSaving}
                onClick={openOverrideWarningModal}
                leadingIcon={DocumentTextIcon}
              >
                {messages('prefillFromPrevious')}
              </Button>
            ) : (
              <div className="flex items-center gap-1.5 py-2.5">
                <span className="text-sm text-gray-500">{messages('editAsCollaboratorLabel')}</span>
                <QuestionMarkCircleIcon className="w-5 text-gray-500" />
              </div>
            )}
          </div>
          {isOverrideWarningModalOpen && (
            <OverrideWarningModal
              report={report}
              open
              onClose={closeOverrideWarningModal}
              onSubmit={handleSubmitModal}
            />
          )}
        </div>
      )}

      {!isMediumScreen && !isAuthor && (
        <span className="text-sm text-gray-500 py-2.5 block">{messages('editAsCollaboratorLabel')}</span>
      )}
    </>
  );
};
