import React from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { PlusIcon, ShapeShiftReportIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { useCreateDraftReport } from 'app/hooks/useCreateDraftReport';
import { useProject } from 'app/queries/projects/projects';
import { useNavigate, useParams } from 'react-router';

type Params = {
  projectId: ProjectSchema['id'];
};

interface ShiftReportsPlaceholderProps {
  status: 'published' | 'draft' | 'archived' | 'inReview';
}

export const ShiftReportsPlaceholder = ({ status }: ShiftReportsPlaceholderProps) => {
  const navigate = useNavigate();
  const { projectId } = useParams<Params>() as Params;
  const messages = useMessageGetter('shiftReport.list.placeholder');
  const newReportButtonTitle = useMessage('shiftReport.new.title');
  const { data: project } = useProject(projectId);
  const createDraftReport = useCreateDraftReport(projectId);
  const isAllowedToCreateShiftReport = !!project?.availableActions.createShiftReport;
  const isArchived = status === 'archived';
  const isInReview = status === 'inReview';
  const placeholderMessages = {
    title: '',
    info: '',
    cta: '',
  };

  if (isAllowedToCreateShiftReport) {
    switch (status) {
      case 'published':
        placeholderMessages.title = messages('noReports');
        placeholderMessages.info = messages('info');
        placeholderMessages.cta = messages('cta');
        break;
      case 'draft':
        placeholderMessages.title = messages('noReportsInProgress');
        placeholderMessages.info = messages('infoInProgress');
        placeholderMessages.cta = messages('cta');
        break;
      case 'archived':
        placeholderMessages.title = messages('noReportsArchived');
        break;
      case 'inReview':
        placeholderMessages.title = messages('noReportsInReview');
        placeholderMessages.info = messages('infoInReview');
    }
  } else {
    switch (status) {
      case 'archived':
        placeholderMessages.title = messages('noReportsArchived');
        break;
      default:
        placeholderMessages.title = messages('noReportsViewer');
        placeholderMessages.info = messages('infoViewer');
        break;
    }
  }

  return (
    <section className="flex h-full flex-1 flex-col items-center justify-center px-4">
      <ShapeShiftReportIcon className="mb-3 w-12 h-12 font-medium text-gray-400 [&_path]:stroke-1" />
      <p className="mb-1 text-sm font-medium leading-5">{placeholderMessages.title}</p>
      {!isArchived && (
        <p className="mb-8 text-center text-sm leading-5 text-gray-500">
          {placeholderMessages.info} <br />
          {placeholderMessages.cta}
        </p>
      )}
      {isInReview && (
        <Button
          color="primary"
          variant="contained"
          size="md"
          data-cy="new-shift-report-button"
          aria-label={messages('ctaInReview')}
          onClick={() => navigate(`/projects/${project?.id}/shift-reports/drafts`)}
        >
          {messages('ctaInReview')}
        </Button>
      )}
      {isAllowedToCreateShiftReport && !isArchived && !isInReview && (
        <Button
          color="primary"
          variant="contained"
          size="md"
          data-cy="new-shift-report-button"
          aria-label={newReportButtonTitle}
          leadingIcon={PlusIcon}
          onClick={() => createDraftReport()}
        >
          {newReportButtonTitle}
        </Button>
      )}
    </section>
  );
};
