import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import { getApiProjectsProjectIdShiftReportsShiftReportIdQueryOptions } from '@shape-construction/api/src/hooks';
import type { ShiftReportSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery, useModal } from '@shape-construction/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { PersonSelect } from 'app/components/Filters/PersonSelect/PersonSelect';
import { useCurrentProject } from 'app/contexts/currentProject';
import type { ControllerRenderProps } from 'react-hook-form';
import type { ShiftReportFormValues } from '../../ShiftReportForm/ShiftReportForm';
import { ChangeApproverConfirmation } from '../ChangeApproverConfirmation/ChangeApproverConfirmation';

type ApproverField = ControllerRenderProps<ShiftReportFormValues, 'approver_id'>;

type ApproverSelectProps = {
  field: ApproverField;
  shiftReport: ShiftReportSchema;
  onChange: (onSuccessCallback?: () => void) => Promise<void>;
};
export const ApproverSelect: React.FC<ApproverSelectProps> = ({ field, shiftReport, onChange }) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('sm'));
  const approvalMessages = useMessageGetter('shiftReport.approvals');
  const currentUserMessage = useMessageGetter('currentUserLabel');
  const queryClient = useQueryClient();
  const project = useCurrentProject();

  const isInReview = shiftReport.state === 'in_review';
  const isApprover = shiftReport.approverId === project.currentTeamMemberId;

  const [preSelectedApproverId, setPreSelectedApproverId] =
    useState<ControllerRenderProps<ShiftReportFormValues, 'approver_id'>['value']>(null);
  const {
    open: isApproverConfirmationOpen,
    openModal: openApproverConfirmation,
    closeModal: closeApproverConfirmation,
  } = useModal(false);

  const handleChangeApprover = (value: number) => {
    if (isInReview && isApprover) {
      setPreSelectedApproverId(value);
      openApproverConfirmation();

      return;
    }

    field.onChange(field.value === value ? null : value);
    onChange();
  };

  const confirmChangeApprover = () => {
    field.onChange(field.value === preSelectedApproverId ? null : preSelectedApproverId);
    onChange(() => {
      queryClient.removeQueries({
        queryKey: getApiProjectsProjectIdShiftReportsShiftReportIdQueryOptions(project.id, shiftReport.id).queryKey,
      });
    });
    setPreSelectedApproverId(null);
  };

  return (
    <>
      <PersonSelect.Root
        value={field.value ?? undefined}
        onChange={(value) => handleChangeApprover(value)}
        disabled={isInReview && !isApprover}
        className={cn({
          'w-full': !isLargeScreen,
        })}
      >
        <PersonSelect.Trigger
          size={isLargeScreen ? 'xs' : 'sm'}
          variant={isLargeScreen ? 'plain' : 'bordered'}
          className={cn({
            'bg-neutral-subtlest': isLargeScreen,
          })}
        >
          {(displayedUser?: UserBasicDetailsSchema) => (
            <PersonSelect.Value placeholder={approvalMessages('unassigned')} value={displayedUser?.name ?? null} />
          )}
        </PersonSelect.Trigger>
        <PersonSelect.Panel>
          <PersonSelect.PanelTitle label={approvalMessages('addApprover')} />
          <PersonSelect.Search placeholder={approvalMessages('search')} />
          <PersonSelect.Options currentPersonLabel={currentUserMessage()} disableSelected={isInReview && isApprover} />
        </PersonSelect.Panel>
      </PersonSelect.Root>
      {isApproverConfirmationOpen && (
        <ChangeApproverConfirmation open onClose={closeApproverConfirmation} onConfirm={confirmChangeApprover} />
      )}
    </>
  );
};
