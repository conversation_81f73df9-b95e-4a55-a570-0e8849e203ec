import React from 'react';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdPeopleTeamMemberIdMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { server } from 'tests/mock-server';
import { render, screen, waitForElementToBeRemoved } from 'tests/test-utils';
import { UserTableCell } from './UserTableCell';

const TestingTable: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <table>
    <tbody>{children}</tbody>
  </table>
);

const userBasicDetails = userBasicDetailsFactory({
  id: 'user-0',
  avatarUrl: 'avatarUrl',
  firstName: 'John',
  lastName: '<PERSON><PERSON>',
  name: '<PERSON>',
});
const user = userFactory(userBasicDetails);

describe('UserTableCell', () => {
  describe('when I am the reporter', () => {
    it('renders (You) text', async () => {
      const projectId = 'project-0';

      server.use(
        getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(() => {
          return teamMemberFactory({
            id: 1,
            projectId,
            user: userBasicDetails,
          });
        })
      );

      render(
        <TestingTable>
          <tr>
            <UserTableCell projectId={projectId} teamMemberId={1} />
          </tr>
        </TestingTable>,
        { user }
      );

      await waitForElementToBeRemoved(() => screen.queryByRole('progressbar', { name: 'Loading reporter' }));
      expect(await screen.findByRole('cell', { name: 'shift reports reporter' })).toHaveTextContent(
        'John Doe shiftReport.list.table.you'
      );
    });
  });

  describe('when I am not the reporter', () => {
    it('does not render (You) text', async () => {
      const reporter = userBasicDetailsFactory({
        id: 'user-1',
        avatarUrl: 'avatarUrl',
        firstName: 'Jane',
        lastName: 'Doe',
        name: 'Jane Doe',
      });
      const projectId = 'project-0';
      server.use(
        getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(() =>
          teamMemberFactory({
            id: 1,
            projectId,
            user: reporter,
          })
        )
      );

      render(
        <TestingTable>
          <tr>
            <UserTableCell projectId={projectId} teamMemberId={1} />
          </tr>
        </TestingTable>,
        { user }
      );

      await waitForElementToBeRemoved(() => screen.queryByRole('progressbar', { name: 'Loading reporter' }));
      expect(await screen.findByRole('cell', { name: 'shift reports reporter' })).toHaveTextContent('Jane Doe');
    });
  });

  describe('when there is no team member', () => {
    it('renders empty user placeholder', async () => {
      render(
        <TestingTable>
          <tr>
            <UserTableCell projectId="project-0" teamMemberId={undefined} />
          </tr>
        </TestingTable>,
        { user }
      );

      expect(await screen.findByRole('cell', { name: 'shift reports reporter' })).toHaveTextContent(
        'shiftReport.list.table.emptyRow'
      );
    });
  });
});
