import React from 'react';
import { booleanFlagFactory, featureFlagsFactory } from '@shape-construction/api/factories/feature-flags';
import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { shiftReportsAvailableActions, shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userFactory } from '@shape-construction/api/factories/users';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/handlers-factories/feature-flags';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdShiftReportsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';
import { server, waitForRequest } from 'tests/mock-server';
import { fakeObserver, render, screen, userEvent, waitFor } from 'tests/test-utils';
import { ShiftReportActionBar } from './ShiftReportActionBar';

describe('ShiftReportActionBar', () => {
  beforeEach(() => {
    window.IntersectionObserver = fakeObserver();
  });

  describe('when Pre-fill button is clicked', () => {
    it('shows the warning modal', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(
        <ShiftReportActionBar
          previousShiftReport={shiftReportsFactory()}
          onOverride={jest.fn()}
          isDraftSaving={false}
        />,
        { history, route }
      );

      await userEvent.click(await screen.findByRole('button', { name: 'shiftReport.new.prefillFromPrevious' }));

      expect(await screen.findByText('shiftReport.modal.overrideTitle')).toBeInTheDocument();
    });

    it('disables when draft is saving', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(
        <ShiftReportActionBar previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} isDraftSaving />,
        { history, route }
      );
      await userEvent.click(await screen.findByRole('button', { name: 'shiftReport.new.prefillFromPrevious' }));

      expect(await screen.findByRole('button', { name: 'shiftReport.new.prefillFromPrevious' })).toBeDisabled();
      expect(screen.queryByText('shiftReport.modal.overrideTitle')).not.toBeInTheDocument();
    });
  });

  describe('when user cannot edit report root fields', () => {
    it('does not render the approver select', async () => {
      const flagData = featureFlagsFactory({
        user: [booleanFlagFactory('shift-report-approvals', true)],
      });
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: false,
        }),
      });
      server.use(
        getApiFeatureFlagsMockHandler(() => flagData),
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(
        <ShiftReportActionBar
          previousShiftReport={shiftReportsFactory()}
          onOverride={jest.fn()}
          isDraftSaving={false}
        />,
        { history, route }
      );

      await waitFor(() => expect(screen.queryByText('shiftReport.approvals.title:')).not.toBeInTheDocument());
    });
  });

  describe('when user can edit report root fields', () => {
    it('renders the approver select', async () => {
      const flagData = featureFlagsFactory({
        user: [booleanFlagFactory('shift-report-approvals', true)],
      });
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiFeatureFlagsMockHandler(() => flagData),
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(
        <ShiftReportActionBar
          previousShiftReport={shiftReportsFactory()}
          onOverride={jest.fn()}
          isDraftSaving={false}
        />,
        { history, route }
      );

      expect(await screen.findByText('shiftReport.approvals.title:')).toBeInTheDocument();
      expect(screen.getByText('shiftReport.approvals.unassigned')).toBeInTheDocument();
    });

    describe('when approvals selector is selected', () => {
      it('opens the approvers list', async () => {
        const flagData = featureFlagsFactory({
          user: [booleanFlagFactory('shift-report-approvals', true)],
        });
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
        });
        const teamMembers = [teamMemberFactory(), teamMemberFactory()];
        server.use(
          getApiFeatureFlagsMockHandler(() => flagData),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

        const { user } = render(
          <ShiftReportActionBar
            previousShiftReport={shiftReportsFactory()}
            onOverride={jest.fn()}
            isDraftSaving={false}
          />,
          { history, route }
        );

        expect(await screen.findByText('shiftReport.approvals.title:')).toBeInTheDocument();
        expect(screen.getByText('shiftReport.approvals.unassigned')).toBeInTheDocument();

        await user.click(screen.getByRole('button', { name: /shiftReport.approvals.unassigned/ }));

        expect(await screen.findAllByRole('option')).toHaveLength(2);
      });

      describe('and clicks on a user from the list', () => {
        it('selects the user as an approver', async () => {
          const flagData = featureFlagsFactory({
            user: [booleanFlagFactory('shift-report-approvals', true)],
          });
          const project = projectFactory({
            id: 'project-0',
            availableActions: projectAvailableActions({
              createShiftReport: true,
            }),
          });
          const shiftReport = shiftReportsFactory({
            availableActions: shiftReportsAvailableActions({
              edit: true,
              editRootFields: true,
            }),
          });
          const teamMembers = [teamMemberFactory({ id: 1 })];
          server.use(
            getApiFeatureFlagsMockHandler(() => flagData),
            getApiProjectsProjectIdMockHandler(() => project),
            getApiProjectsProjectIdShiftReportsMockHandler(),
            getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
            getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
          );
          const patchShiftReport = waitForRequest('PATCH', '/api/projects/:projectId/shift_reports/:shiftReportId');
          const history = createMemoryHistory({
            initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
          });
          const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

          const { user } = render(
            <ShiftReportActionBar
              previousShiftReport={shiftReportsFactory()}
              onOverride={jest.fn()}
              isDraftSaving={false}
            />,
            { history, route }
          );

          expect(await screen.findByText('shiftReport.approvals.title:')).toBeInTheDocument();
          expect(screen.getByText('shiftReport.approvals.unassigned')).toBeInTheDocument();

          await user.click(screen.getByRole('button', { name: /shiftReport.approvals.unassigned/ }));

          await user.click(await screen.findByRole('option'));

          const req = await patchShiftReport;
          expect(await req.json()).toEqual({
            approver_id: 1,
          });
        });
      });

      describe('and clicks on an already selected user', () => {
        it('unassigns the approver', async () => {
          const flagData = featureFlagsFactory({
            user: [booleanFlagFactory('shift-report-approvals', true)],
          });
          const project = projectFactory({
            id: 'project-0',
            availableActions: projectAvailableActions({
              createShiftReport: true,
            }),
          });
          const shiftReport = shiftReportsFactory({
            availableActions: shiftReportsAvailableActions({
              edit: true,
              editRootFields: true,
            }),
          });
          const teamMembers = [teamMemberFactory({ id: 1 })];
          server.use(
            getApiFeatureFlagsMockHandler(() => flagData),
            getApiProjectsProjectIdMockHandler(() => project),
            getApiProjectsProjectIdShiftReportsMockHandler(),
            getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
            getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
          );
          const history = createMemoryHistory({
            initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
          });
          const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

          const { user } = render(
            <ShiftReportActionBar
              previousShiftReport={shiftReportsFactory()}
              onOverride={jest.fn()}
              isDraftSaving={false}
            />,
            { history, route, hookFormValues: { approver_id: 1 } }
          );

          expect(await screen.findByText('shiftReport.approvals.title:')).toBeInTheDocument();
          expect(screen.getByText('shiftReport.approvals.unassigned')).toBeInTheDocument();

          await user.click(screen.getByRole('button', { name: /shiftReport.approvals.unassigned/ }));

          await user.click(await screen.findByRole('option'));

          expect(await screen.findByRole('button', { name: /shiftReport.approvals.unassigned/ })).toBeInTheDocument();
        });
      });
    });
  });

  describe('when report in in review', () => {
    describe('and approver tries to change the approver', () => {
      it('opens a confirmation dialog and confirms selection', async () => {
        const flagData = featureFlagsFactory({
          user: [booleanFlagFactory('shift-report-approvals', true)],
        });
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
          currentTeamMemberId: 1,
        });
        const shiftReport = shiftReportsFactory({
          state: 'in_review',
          approverId: 1,
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
        });
        const teamMembers = [
          teamMemberFactory({ id: 1, user: userFactory({ name: 'John Doe' }) }),
          teamMemberFactory({ id: 2, user: userFactory({ name: 'Jane Schmit' }) }),
        ];
        server.use(
          getApiFeatureFlagsMockHandler(() => flagData),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
        );
        const patchShiftReport = waitForRequest('PATCH', '/api/projects/:projectId/shift_reports/:shiftReportId');
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

        const { user } = render(
          <ShiftReportActionBar
            previousShiftReport={shiftReportsFactory()}
            onOverride={jest.fn()}
            isDraftSaving={false}
          />,
          { history, route, pageData: { project } }
        );

        expect(await screen.findByText('shiftReport.approvals.title:')).toBeInTheDocument();
        expect(screen.getByText('shiftReport.approvals.unassigned')).toBeInTheDocument();
        await user.click(screen.getByRole('button', { name: /shiftReport.approvals.unassigned/ }));
        await user.click(await screen.findByLabelText('Jane Schmit'));

        expect(await screen.findByText('shiftReport.changeApprovalConfirmation.title'));
        await user.click(screen.getByRole('button', { name: 'shiftReport.changeApprovalConfirmation.confirm' }));
        const req = await patchShiftReport;
        expect(await req.json()).toEqual({
          approver_id: 2,
        });
      });
    });
  });
});
