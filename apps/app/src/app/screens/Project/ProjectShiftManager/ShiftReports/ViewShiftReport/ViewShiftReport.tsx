import React, { useLayoutEffect, useMemo } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import Alert from '@shape-construction/arch-ui/src/Alert';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Button from '@shape-construction/arch-ui/src/Button';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import {
  DocumentArrowDownIcon,
  GlobeAmericasIcon,
  InboxArrowDownIcon,
  LockClosedIcon,
  PencilIcon,
  ShareIcon,
  TrashIcon,
  UsersIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import ButtonLink from '@shape-construction/arch-ui/src/Link';
import Page from '@shape-construction/arch-ui/src/Page';
import Popover from '@shape-construction/arch-ui/src/Popover';
import Tabs from '@shape-construction/arch-ui/src/Tabs';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { SharePopover } from 'app/components/SharePopover/SharePopover';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useCurrentShiftReport } from 'app/contexts/currentShiftReport';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { useShiftReportExport, useSubmitForReviewShiftReport } from 'app/queries/shiftReports/shiftReports';
import { Link, Outlet, useLocation } from 'react-router';
import { useTeamsSubscriptionPlan } from '../../../../../queries/teamsSubscriptionPlan/teamsSubscriptionPlan';
import { ArchiveConfirmationModal } from '../components/ArchiveConfirmationModal/ArchiveConfirmationModal';
import { DeleteDraftConfirmationModal } from '../components/DeleteDraftConfirmationModal/DeleteDraftConfirmationModal';
import { getLabelByReportQualityPercentage } from '../components/getLabelByReportQualityPercentage';
import { ReportPublishCTA } from '../components/ReportPublishCTA/ReportPublishCTA';
import { RestoreConfirmationModal } from '../components/RestoreConfirmationModal/RestoreConfirmationModal';
import {
  qualityLabelBadgeThemeMap,
  ShiftReportQualityIndicatorsInfoTable,
} from '../components/ShiftReportQualityIndicatorsInfoTable';
import { ExportOptionsModal } from '../PublishAndOrExportShiftReport/ExportOptionsModal';
import { singleReportExportOptions } from '../PublishAndOrExportShiftReport/exportOptionsUtils';
import { PublishConfirmationModal } from '../PublishAndOrExportShiftReport/PublishConfirmationModal';
import { ShiftReportForm, type ShiftReportFormComponentProps } from '../ShiftReportForm/ShiftReportForm';
import { useShiftReportTabs } from './hooks/use-shift-report-tabs';
import { usePublishModal } from './hooks/usePublishModal';

const ViewShiftReportPrivate = ({ handleSubmit, trigger }: ShiftReportFormComponentProps) => {
  const location = useLocation();
  const qualityLabelMessages = useMessageGetter('shiftReport.qualityLabel');
  const messages = useMessageGetter('shiftReport.view');
  const approvalMessages = useMessageGetter('shiftReport.approvals');
  const visibilityMessages = useMessageGetter('shiftReport.visibility');
  const shareMessages = useMessageGetter('shiftReport.shareModal');
  const { setLayoutConfig } = useLayoutContext();
  const { mutate: reviewShiftReport } = useSubmitForReviewShiftReport();
  const project = useCurrentProject();
  const teamId = project?.currentTeamId;
  const { data: teamSubscriptionData } = useTeamsSubscriptionPlan(project.id, teamId!);
  const shiftReport = useCurrentShiftReport();
  const { isPending } = useShiftReportExport();
  const { shiftReportTabs } = useShiftReportTabs();
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const isExportAvailable = Boolean(teamSubscriptionData?.features?.exportShiftReportsData?.available);
  const backNavigationTitle = useMessage('navigation.backTo', { route: 'shift reports' });
  const qualityLabel = shiftReport?.completionQualityScore
    ? getLabelByReportQualityPercentage(shiftReport?.completionQualityScore)
    : null;

  const {
    closeArchiveModal,
    closeRestoreModal,
    handleCloseExportOptionsModal,
    handleClosePublishModal,
    handleExport,
    handleOpenExportOptionsModal,
    handlePublishAndExport,
    handlePublishAttempt,
    isArchiveModalOpen,
    isExportOptionsModalOpen,
    isPublishModalOpen,
    isRestoreModalOpen,
    openArchiveModal,
    openExportOptionsModal,
    openRestoreModal,
    goBackToShiftReports,
    isDeleteModalOpen,
    closeDeleteModal,
    openDeleteModal,
  } = usePublishModal({
    projectId: project.id,
    shiftReportId: shiftReport.id,
    trigger,
  });

  const handleBack = () => goBackToShiftReports();

  const handleReview = async () => {
    reviewShiftReport(
      { projectId: project.id, shiftReportId: shiftReport.id },
      {
        onSuccess: () => {
          showSuccessToast({
            message: approvalMessages('successReviewToast'),
          });
        },
      }
    );
  };

  const webShareData = {
    title: shareMessages('shareLinkTitle', { title: shiftReport?.reportTitle ?? 'Untitled' }),
    text: shareMessages('shareLinkContent', { title: shiftReport?.reportTitle ?? 'Untitled' }),
    url: window.location.href,
  };
  const ShareButton = (
    <div className="pt-[2px]">
      <SharePopover title={shareMessages('title')} content={shareMessages('content')} shareData={webShareData}>
        <IconButton
          color="secondary"
          variant="text"
          size="sm"
          icon={ShareIcon}
          aria-label={messages('shareCTA')}
          title={messages('shareCTA')}
        />
      </SharePopover>
    </div>
  );

  const Actions = shiftReport?.availableActions.edit ? (
    <div className="flex items-start gap-2">
      {ShareButton}
      {shiftReport?.availableActions.delete && (
        <div className="pr-1 pt-[2px]">
          <IconButton
            color="secondary"
            variant="text"
            size="sm"
            icon={TrashIcon}
            aria-label={messages('deleteCTA')}
            title={messages('deleteCTA')}
            onClick={openDeleteModal}
          />
        </div>
      )}
      <Link
        to={`/projects/${project.id}/shift-reports/${shiftReport.id}/edit`}
        state={location.state}
        replace={isLargeScreen}
      >
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          data-cy="save-edit-shift-report-button"
          aria-label={messages('edit')}
          leadingIcon={isLargeScreen ? PencilIcon : undefined}
        >
          {messages('edit')}
        </Button>
      </Link>
      {shiftReport?.availableActions.publish && (
        <ReportPublishCTA
          shiftReport={shiftReport}
          project={project}
          publish={handleSubmit(handlePublishAttempt)}
          submitToReview={handleReview}
        />
      )}
    </div>
  ) : (
    <div className="flex justify-end align-center gap-3">
      {ShareButton}
      {shiftReport?.availableActions.archive && (
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          disabled={isPending}
          aria-label={messages('archiveCTA')}
          leadingIcon={isLargeScreen ? InboxArrowDownIcon : undefined}
          onClick={openArchiveModal}
        >
          {messages('archiveCTA')}
        </Button>
      )}
      {shiftReport?.availableActions.export && (
        <div className="flex items-start space-x-2">
          <Button
            color="primary"
            variant="contained"
            size="md"
            data-cy="publish-shift-report-button"
            disabled={isPending}
            aria-label={messages('exportCTA')}
            leadingIcon={isLargeScreen ? DocumentArrowDownIcon : undefined}
            onClick={openExportOptionsModal}
          >
            {messages('exportCTA')}
          </Button>
        </div>
      )}
    </div>
  );

  const element = useMemo(() => {
    let Icon: React.FC<React.ComponentProps<'svg'>> | null;
    let text: string;
    switch (shiftReport?.visibility) {
      case 'private':
        Icon = LockClosedIcon;
        text = visibilityMessages('private');
        break;
      case 'public':
        Icon = GlobeAmericasIcon;
        text = visibilityMessages('public');
        break;
      case 'specific_teams':
        Icon = UsersIcon;
        text = visibilityMessages('specificTeams');
        break;
      default:
        Icon = null;
        text = '';
        break;
    }
    const icon = Icon && <Icon className="h-4 text-gray-400" />;
    const label = <p className="text-gray-900">{text}</p>;
    return {
      icon,
      label,
    };
  }, [shiftReport?.visibility, visibilityMessages]);

  const title = shiftReport?.reportTitle || `Report ${shiftReport?.reportDate}`;

  useLayoutEffect(() => {
    setLayoutConfig({
      ...LayoutConfigs.initialVariant,
      showBottomNavigation: false,
    });
  }, [setLayoutConfig]);

  return (
    <div className="h-full">
      <Page data-cy="view-shift-report" className="h-screen md:h-full">
        {shiftReport?.archived && shiftReport?.availableActions.restore && (
          <Alert color="warning" rounded={false}>
            <Alert.Message data-cy="archived-shift-report-message">{messages('archivedMessage')}</Alert.Message>
            <Alert.Actions data-cy="archived-shift-report-actions">
              <ButtonLink color="warning" as="button" onClick={openRestoreModal}>
                {messages('restoreCTA')}
              </ButtonLink>
            </Alert.Actions>
          </Alert>
        )}
        <Page.Header
          className="bg-white pb-2 shadow-none"
          title={title}
          backNavigationTitle={backNavigationTitle}
          bottomSection={
            <div className="flex items-center space-x-5">
              <div className="flex items-center space-x-2">
                {element.icon}
                {element.label}
              </div>
              {qualityLabel && (
                <div className="flex space-x-2 items-center">
                  <Badge label={qualityLabelMessages(qualityLabel)} theme={qualityLabelBadgeThemeMap[qualityLabel]} />
                  <Popover.Root>
                    <Popover.Trigger>
                      <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
                    </Popover.Trigger>
                    <Popover.Content
                      hideArrow
                      side="bottom"
                      align="start"
                      className="md:max-w-xs md:p-0 flex flex-row items-center"
                    >
                      <ShiftReportQualityIndicatorsInfoTable />
                    </Popover.Content>
                  </Popover.Root>
                </div>
              )}
            </div>
          }
          hasBackNavigation
          onBackNavigation={handleBack}
          rightSection={Actions}
        />
        <Tabs selectedValue={location.pathname}>
          {shiftReportTabs.map(({ enabled, icon: Icon, label, path }) => {
            if (!enabled) return null;

            return (
              <Link key={label} to={path} state={location.state} replace>
                <Tabs.Tab selected={path === location.pathname}>
                  <span className="mr-2 h-5 w-5">
                    <Icon />
                  </span>
                  {label}
                </Tabs.Tab>
              </Link>
            );
          })}
        </Tabs>

        <Outlet context={{ shiftReport }} />

        <PublishConfirmationModal
          open={isPublishModalOpen}
          handlePublish={() => handlePublishAndExport()}
          handlePublishAndExport={handleOpenExportOptionsModal}
          onClose={handleClosePublishModal}
        />
        <ExportOptionsModal
          isOpen={isExportOptionsModalOpen}
          onClose={handleCloseExportOptionsModal}
          handleExportReport={(fileType) =>
            shiftReport?.publishedAt ? handleExport(fileType) : handlePublishAndExport(fileType)
          }
          options={singleReportExportOptions(!isExportAvailable)}
        />
        <ArchiveConfirmationModal
          isOpen={isArchiveModalOpen}
          onClose={closeArchiveModal}
          shiftReportId={shiftReport.id}
        />
        <RestoreConfirmationModal
          isOpen={isRestoreModalOpen}
          onClose={closeRestoreModal}
          shiftReportId={shiftReport.id}
        />
        <DeleteDraftConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={closeDeleteModal}
          shiftReportId={shiftReport.id}
        />
      </Page>
    </div>
  );
};

export const ViewShiftReport = () => {
  return <ShiftReportForm component={ViewShiftReportPrivate} />;
};

export { ViewShiftReport as Component };
