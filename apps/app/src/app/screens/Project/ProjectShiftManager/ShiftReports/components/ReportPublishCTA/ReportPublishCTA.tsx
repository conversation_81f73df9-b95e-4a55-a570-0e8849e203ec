import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { CheckCircleIcon, CheckIcon, DocumentMagnifyingGlassIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useMediaQuery } from '@shape-construction/hooks';

type PublishButtonProps = {
  disabled: boolean;
  onClick: React.MouseEventHandler<HTMLButtonElement>;
};
const PublishButton: React.FC<PublishButtonProps> = ({ disabled, onClick }) => {
  const messages = useMessageGetter('shiftReport.new');
  const isLargeScreen = useMediaQuery(breakpoints.up('sm'));

  return (
    <Button
      color="primary"
      variant="contained"
      size="md"
      data-cy="publish-shift-report-button"
      aria-label={messages('publish')}
      leadingIcon={isLargeScreen ? CheckIcon : undefined}
      disabled={disabled}
      onClick={onClick}
    >
      {messages('publish')}
    </Button>
  );
};

type SubmitApprovalButtonProps = {
  onClick: React.MouseEventHandler<HTMLButtonElement>;
};
const SubmitApprovalButton: React.FC<SubmitApprovalButtonProps> = ({ onClick }) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('sm'));
  const approvalMessages = useMessageGetter('shiftReport.approvals');

  return (
    <Button
      color="primary"
      variant="contained"
      size="md"
      aria-label={approvalMessages('submitForApproval')}
      leadingIcon={isLargeScreen ? CheckCircleIcon : undefined}
      onClick={onClick}
    >
      {approvalMessages('submitForApproval')}
    </Button>
  );
};

const PendingApprovalButton: React.FC = () => {
  const isLargeScreen = useMediaQuery(breakpoints.up('sm'));
  const approvalMessages = useMessageGetter('shiftReport.approvals');

  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <Button
          color="primary"
          variant="contained"
          size="md"
          aria-label={approvalMessages('pendingApproval')}
          leadingIcon={isLargeScreen ? DocumentMagnifyingGlassIcon : undefined}
          disabled
        >
          {approvalMessages('pendingApproval')}
        </Button>
      </Tooltip.Trigger>
      <Tooltip.Content side="bottom" className="z-popover">
        {approvalMessages('inReviewTooltip')}
      </Tooltip.Content>
    </Tooltip.Root>
  );
};

type ApprovePublishProps = {
  onClick: React.MouseEventHandler<HTMLButtonElement>;
};
const ApprovePublishButton: React.FC<ApprovePublishProps> = ({ onClick }) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('sm'));
  const approvalMessages = useMessageGetter('shiftReport.approvals');

  return (
    <Button
      color="success"
      variant="contained"
      size="md"
      aria-label={approvalMessages('approvePublish')}
      leadingIcon={isLargeScreen ? CheckCircleIcon : undefined}
      onClick={onClick}
    >
      {approvalMessages('approvePublish')}
    </Button>
  );
};

type ReportPublishCTAProps = {
  shiftReport: ShiftReportSchema;
  project: ProjectSchema;
  publish: () => void;
  submitToReview: () => void;
};
export const ReportPublishCTA: React.FC<ReportPublishCTAProps> = ({
  shiftReport,
  project,
  publish,
  submitToReview,
}) => {
  const { value: shiftReportApprovalsEnabled } = useFeatureFlag('shift-report-approvals');
  const isAuthor = shiftReport.availableActions.editRootFields;
  const canPublish = shiftReport.availableActions.publish;
  const isApproval = shiftReport.approverId === project.currentTeamMemberId;
  const canSubmitToReview = shiftReport.availableActions.submitForReview;
  const isDraft = shiftReport.state === 'draft';
  const isInReview = shiftReport.state === 'in_review';

  if (shiftReportApprovalsEnabled) {
    if (isDraft && isAuthor && canSubmitToReview) return <SubmitApprovalButton onClick={submitToReview} />;

    if (isInReview && isApproval) return <ApprovePublishButton onClick={publish} />;

    if (isInReview && isAuthor) return <PendingApprovalButton />;
  }

  return <PublishButton disabled={!isAuthor || !canPublish} onClick={publish} />;
};
