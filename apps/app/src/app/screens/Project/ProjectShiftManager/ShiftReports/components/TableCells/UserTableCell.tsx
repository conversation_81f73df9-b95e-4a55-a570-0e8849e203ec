import React, { Suspense } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import { SkeletonText } from '@shape-construction/arch-ui/src/Skeleton';
import Table from '@shape-construction/arch-ui/src/Table';
import type { TableCellProps } from '@shape-construction/arch-ui/src/Table/components/TableCell';
import { useSuspenseQuery } from '@tanstack/react-query';
import { getProjectPersonQueryOptions } from 'app/queries/projects/people';
import { useCurrentUser } from 'app/queries/users/users';

type UserTableCellProps = {
  projectId: ProjectSchema['id'];
  teamMemberId?: ShiftReportBasicDetailsSchema['teamMemberId'];
} & TableCellProps;
export const UserTableCell = ({ projectId, teamMemberId, ...restProps }: UserTableCellProps) => {
  const messages = useMessageGetter('shiftReport.list.table');

  return (
    <Table.Cell aria-label="shift reports reporter" className="px-4" {...restProps}>
      <Suspense
        fallback={
          <div role="progressbar" aria-label="Loading reporter">
            <SkeletonText animation="pulse" size="xxs" />
          </div>
        }
      >
        {teamMemberId ? <UserContent projectId={projectId} teamMemberId={teamMemberId} /> : messages('emptyRow')}
      </Suspense>
    </Table.Cell>
  );
};

type UserContentProps = {
  projectId: ProjectSchema['id'];
  teamMemberId: ShiftReportBasicDetailsSchema['teamMemberId'];
} & React.HTMLAttributes<HTMLDivElement>;
export const UserContent = ({
  projectId,
  teamMemberId,
  ...restProps
}: UserContentProps & { teamMemberId: UserContentProps['teamMemberId'] }) => {
  const messages = useMessageGetter('shiftReport.list.table');
  const { data: teamMember } = useSuspenseQuery(getProjectPersonQueryOptions(projectId, teamMemberId));
  const user = teamMember?.user;

  const currentUser = useCurrentUser();
  const isYou = currentUser.id === user?.id;
  const highlightedColor = isYou ? 'primary' : undefined;
  const userAvatar = user
    ? {
        id: user.id,
        name: user.name,
        firstName: user.firstName ?? undefined,
        lastName: user.lastName ?? undefined,
        avatarUrl: user.avatarUrl ?? undefined,
      }
    : undefined;

  const isUserRemoved = user === undefined;

  return (
    <div className="flex items-center gap-x-2" {...restProps}>
      <UserAvatar highlighted highlightedColor={highlightedColor} user={userAvatar} size="sm" />
      <p>
        {isUserRemoved ? (
          <span className="text-gray-500">{messages('removedUser')}</span>
        ) : (
          <>
            <span className="font-medium text-gray-900">{user.name}</span>
            {isYou && <span className="text-gray-500"> {messages('you')}</span>}
          </>
        )}
      </p>
    </div>
  );
};
