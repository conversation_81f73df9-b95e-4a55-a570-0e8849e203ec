import React from 'react';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import { shiftReportsListItemFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { getApiProjectsProjectIdShiftReportsInReviewMockHandler } from '@shape-construction/api/src/mock-handlers';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, waitForElementToBeRemoved } from 'tests/test-utils';
import { InReviewShiftReportsTable } from './InReviewShiftReportsTable';

describe('<InReviewShiftReportsTable />', () => {
  describe('when there are in review shift reports', () => {
    it('renders in review shift reports table rows', async () => {
      const project = projectFactory({
        id: 'project-0',
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsInReviewMockHandler({
          shiftReports: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory(),
        }),
        getApiProjectsProjectIdPeopleMockHandler(() => {
          const projectId = project.id;
          const teamMembers = [
            teamMemberFactory({
              id: 1,
              projectId,
              status: 'joined',
              user: userBasicDetailsFactory({ name: 'John Doe' }),
            }),
          ];
          return teamMembers;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports'],
      });
      const route = { path: '/projects/:projectId/shift-reports' };

      render(<InReviewShiftReportsTable />, { history, route });

      await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
      expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.date' })).toBeInTheDocument();
      expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
    });
  });
});
