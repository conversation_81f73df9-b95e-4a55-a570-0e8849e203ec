import React from 'react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useQueryClient } from '@tanstack/react-query';
import type { AnalyticEvent, ShiftReportEventName } from 'app/analytics/eventsTypes';
import { useCreateDraftReport } from 'app/hooks/useCreateDraftReport';
import { useSendAnalyticsEvent } from 'app/queries/analytics/analytics';
import { getShiftReportQueryOptions } from 'app/queries/shiftReports/shiftReports';
import { useNavigate } from 'react-router';
import { type Action, ActionsContent, ActionsTableCell } from '../../components/TableCells/ActionsTableCell';
import { DateContent, DateTableCell } from '../../components/TableCells/DateTableCell';
import { TextContent, TextTableCell } from '../../components/TableCells/TextTableCell';
import { UserContent, UserTableCell } from '../../components/TableCells/UserTableCell';

export type ShiftReportAnalyticEvent = AnalyticEvent<ShiftReportEventName>;

type PublishedShiftReportsTableRowProps = {
  rowData: ShiftReportBasicDetailsSchema;
  project: ProjectSchema;
  isLargeScreen: boolean;
  handleOpenArchiveModal: (rowDataId: ShiftReportBasicDetailsSchema['id']) => void;
};

export const PublishedShiftReportsTableRow: React.FC<PublishedShiftReportsTableRowProps> = ({
  rowData,
  project,
  isLargeScreen,
  handleOpenArchiveModal,
}) => {
  const { value: isShiftReportApprovalsEnabled } = useFeatureFlag('shift-report-approvals');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const createDraftReport = useCreateDraftReport(project.id, true);
  const { mutate: sendAnalyticsEvent } = useSendAnalyticsEvent();

  const handleRowClick = (event: React.SyntheticEvent) => {
    event.preventDefault();
    navigate(`/projects/${project.id}/shift-reports/${rowData.id}`, {
      state: { tab: '' },
    });
  };

  const handleDuplicate = async (event: React.SyntheticEvent) => {
    event.stopPropagation();
    const template = await queryClient.ensureQueryData(getShiftReportQueryOptions(project.id, rowData.id));
    createDraftReport(template);
    sendAnalyticsEvent({ event_name: 'duplicate_shift_report' } as ShiftReportAnalyticEvent);
  };

  const handleArchive = async (event: React.SyntheticEvent) => {
    event.stopPropagation();
    handleOpenArchiveModal(rowData.id);
  };

  const isAllowedToCreateShiftReport = !!project?.availableActions.createShiftReport;
  const isAllowedToArchiveShiftReport = !!rowData?.availableActions.archive;
  const actions: Action[] = [
    { type: 'duplicate', hidden: !isAllowedToCreateShiftReport, onClick: handleDuplicate },
    { type: 'archive', hidden: !isAllowedToArchiveShiftReport, onClick: handleArchive },
  ];

  if (isLargeScreen) {
    return (
      <Table.Row key={rowData.id} onClick={handleRowClick}>
        <DateTableCell date={rowData.reportDate} />
        <TextTableCell text={rowData.shiftType} />
        {isShiftReportApprovalsEnabled && (
          <UserTableCell projectId={project.id} teamMemberId={rowData.approverId ?? undefined} />
        )}
        <UserTableCell projectId={project.id} teamMemberId={rowData.teamMemberId} />
        <TextTableCell text={rowData.contractorName} />
        <ActionsTableCell actions={actions} hidden={!isAllowedToCreateShiftReport && !isAllowedToArchiveShiftReport} />
      </Table.Row>
    );
  }

  return (
    <Table.Row key={rowData.id} onClick={handleRowClick}>
      <Table.Cell className="flex justify-between items-start">
        <div className="flex grow flex-col gap-3">
          <div className="flex gap-6">
            <DateContent date={rowData.reportDate} />
            <TextContent text={rowData.shiftType} />
          </div>
          <UserContent projectId={project.id} teamMemberId={rowData.teamMemberId} />
          <TextContent text={rowData.contractorName} />
        </div>
        <ActionsContent actions={actions} />
      </Table.Cell>
    </Table.Row>
  );
};
