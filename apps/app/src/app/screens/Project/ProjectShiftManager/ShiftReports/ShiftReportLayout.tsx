import React, { useEffect } from 'react';
import { getApiProjectsProjectIdShiftReportsShiftReportIdQueryOptions } from '@shape-construction/api/src/hooks';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import { useQuery } from '@tanstack/react-query';
import type { ResponseError } from 'app/axios';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { CurrentShiftReportProvider } from 'app/contexts/currentShiftReport';
import {
  UnavailableErrorModal,
  useUnavailableErrorModal,
} from 'app/screens/Project/ProjectShiftManager/ShiftReports/components/UnavailableErrorModal/UnavailableErrorModal';
import { Outlet, useParams } from 'react-router';
import { NotAuthorizedReport } from './components/NotAuthorizedReport/NotAuthorizedReport';

type Params = { shiftReportId: ShiftReportSchema['id'] };

export const ShiftReportLayout: React.FC = () => {
  const { shiftReportId } = useParams<Params>() as Params;
  const project = useCurrentProject();
  const {
    data: shiftReport,
    isLoading,
    error: shiftReportError,
  } = useQuery({
    ...getApiProjectsProjectIdShiftReportsShiftReportIdQueryOptions(project.id, shiftReportId),
    refetchOnMount: 'always',
  });
  const unavailableErrorModal = useUnavailableErrorModal();

  useEffect(() => {
    if ((shiftReportError as ResponseError)?.response?.status === 404) {
      unavailableErrorModal.open();
    }
  }, [shiftReportError, unavailableErrorModal]);

  if (isLoading) return <LoadingSpinner variant="screen" />;

  if (!shiftReport && (shiftReportError as ResponseError)?.response?.status !== 404) return <NotAuthorizedReport />;

  return (
    <>
      {shiftReport && (
        <CurrentShiftReportProvider shiftReport={shiftReport}>
          <Outlet />
        </CurrentShiftReportProvider>
      )}
      {unavailableErrorModal.props.isOpen && <UnavailableErrorModal editing {...unavailableErrorModal.props} isOpen />}
    </>
  );
};

export { ShiftReportLayout as Component };
