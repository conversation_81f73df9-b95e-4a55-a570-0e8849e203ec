import { createMemoryHistory } from 'history';
import { render, screen } from 'tests/test-utils';
import { NotAuthorizedReport } from './NotAuthorizedReport';

describe('<NotAuthorizedReport />', () => {
  it('renders correctly', () => {
    render(<NotAuthorizedReport />);

    expect(screen.getByText('shiftReport.notAuthorizedReport.title')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.notAuthorizedReport.body')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.notAuthorizedReport.goBack')).toBeInTheDocument();
  });

  describe('when user clicks on go back button', () => {
    it('navigates to shift reports page', async () => {
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId' };
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0'],
      });

      const { user } = render(<NotAuthorizedReport />, { history, route });
      await user.click(screen.getByRole('button', { name: 'shiftReport.notAuthorizedReport.goBack' }));

      expect(history.location.pathname).toBe('/projects/project-0/shift-reports');
    });
  });
});
