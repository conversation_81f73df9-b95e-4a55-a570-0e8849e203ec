import { render, screen } from 'tests/test-utils';
import { ChangeApproverConfirmation } from './ChangeApproverConfirmation';

describe('<ChangeApproverConfirmation />', () => {
  it('renders correctly', () => {
    render(<ChangeApproverConfirmation open onClose={jest.fn()} onConfirm={jest.fn()} />);

    expect(screen.getByText('shiftReport.changeApprovalConfirmation.title'));
    expect(screen.getByText('shiftReport.changeApprovalConfirmation.subTitle'));
    expect(screen.getByText('shiftReport.changeApprovalConfirmation.firstPoint'));
    expect(screen.getByText('shiftReport.changeApprovalConfirmation.secondPoint'));
    expect(screen.getByText('shiftReport.changeApprovalConfirmation.cancel'));
    expect(screen.getByText('shiftReport.changeApprovalConfirmation.confirm'));
  });

  describe('when confirm button is clicked', () => {
    it('calls onConfirm prop', async () => {
      const confirmMock = jest.fn();

      const { user } = render(<ChangeApproverConfirmation open onClose={jest.fn()} onConfirm={confirmMock} />);
      await user.click(screen.getByRole('button', { name: 'shiftReport.changeApprovalConfirmation.confirm' }));

      expect(confirmMock).toHaveBeenCalledTimes(1);
    });
  });
});
