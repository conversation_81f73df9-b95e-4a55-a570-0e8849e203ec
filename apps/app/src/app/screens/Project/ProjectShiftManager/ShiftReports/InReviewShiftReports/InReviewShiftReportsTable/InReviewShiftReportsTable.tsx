import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { keepPreviousData } from '@tanstack/react-query';
import { useCurrentProject } from 'app/contexts/currentProject';
import { usePaginationQueryParams } from 'app/hooks/usePaginationQueryParams';
import { useShiftReportsInReview } from 'app/queries/shiftReports/shiftReports';
import { useParams } from 'react-router';
import { ShiftReportsPlaceholder } from '../../components/ShiftReportsPlaceholder';
import { ShiftReportsTableSkeleton } from '../../components/ShiftReportsTableSkeleton';
import { SortableTableHeader } from '../../components/SortableTableHeader';
import { TablePagination } from '../../components/TablePagination';
import { InReviewShiftReportsTableRow } from './InReviewShiftReportsTableRow';

type Params = {
  projectId: ProjectSchema['id'];
};

export const InReviewShiftReportsTable = () => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { projectId } = useParams<Params>() as Params;
  const project = useCurrentProject();
  const pagination = usePaginationQueryParams();
  const params = { after: pagination.after, before: pagination.before };
  const { data: reportsData, isLoading } = useShiftReportsInReview(projectId, params, {
    query: { placeholderData: keepPreviousData },
  });
  const tableMessages = useMessageGetter('shiftReport.list.table');

  if (isLoading) return <ShiftReportsTableSkeleton />;

  if (!reportsData || !project) return null;

  if (!isLoading && !reportsData.shiftReports?.length) {
    return <ShiftReportsPlaceholder status="inReview" />;
  }

  return (
    <Table.Container data-cy="in-review-shift-reports" className="container max-w-7xl">
      <Table>
        {isLargeScreen && (
          <Table.Heading>
            <Table.Row>
              <SortableTableHeader label={tableMessages('date')} />
              <SortableTableHeader label={tableMessages('shift')} />
              <SortableTableHeader label={tableMessages('approver')} />
              <SortableTableHeader label={tableMessages('author')} />
              <SortableTableHeader label={tableMessages('team')} />
              <Table.Header />
            </Table.Row>
          </Table.Heading>
        )}
        <Table.Body className="[&_tr]:cursor-pointer [&_tr]:border-b [&_tr]:bg-white [&_tr]:last:border-b-0">
          {reportsData.shiftReports.map((report) => (
            <InReviewShiftReportsTableRow
              key={report.id}
              rowData={report}
              project={project}
              isLargeScreen={isLargeScreen}
            />
          ))}
        </Table.Body>
        <TablePagination
          meta={reportsData.meta}
          count={reportsData.shiftReports.length}
          onNext={pagination.onNext}
          onPrevious={pagination.onPrevious}
        />
      </Table>
    </Table.Container>
  );
};
