import React, { useEffect } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import Page from '@shape-construction/arch-ui/src/Page';
import Tabs from '@shape-construction/arch-ui/src/Tabs/ScrollableTabs';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { ActivitiesToolbar } from 'app/components/ShiftManager/Activities/ActivitiesToolbar/ActivitiesToolbar';
import { FeatureLimits } from 'app/components/SubscriptionPlanFeatures/FeatureLimits/FeatureLimits';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { Outlet, useLocation, useNavigate, useParams } from 'react-router';
import { ShiftManagerHeader } from '../../../components/ShiftManager/ShiftManagerHeader/ShiftManagerHeader';

type Params = {
  projectId: ProjectSchema['id'];
  shiftReportId: ShiftReportBasicDetailsSchema['id'];
};

export const ProjectShiftManager = () => {
  const { value: isShiftReportApprovalsEnabled } = useFeatureFlag('shift-report-approvals');
  const { setLayoutConfig } = useLayoutContext();
  const { projectId } = useParams<Params>() as Params;
  const location = useLocation();
  const navigate = useNavigate();
  const messages = useMessageGetter('shiftManager.tabs');

  useEffect(() => {
    setLayoutConfig({ ...LayoutConfigs.initialVariant, showFilter: false });
  }, [setLayoutConfig]);

  const isOnActivitiesTab = location.pathname.includes('activities');

  return (
    <Page data-cy="shift-reports-page">
      <ShiftManagerHeader isOnActivitiesTab={isOnActivitiesTab} />
      <div data-cy="shift-reports-tabs">
        <Tabs selectedValue={location.pathname} onChange={(_, url) => navigate(url)}>
          <Tabs.Tab value={`/projects/${projectId}/shift-reports`}>{messages('shiftReports.published')}</Tabs.Tab>
          {isShiftReportApprovalsEnabled && (
            <Tabs.Tab value={`/projects/${projectId}/shift-reports/review`}>
              {messages('shiftReports.inReview')}
            </Tabs.Tab>
          )}
          <Tabs.Tab value={`/projects/${projectId}/shift-reports/drafts`}>
            {messages('shiftReports.inProgress')}
          </Tabs.Tab>
          <Tabs.Tab value={`/projects/${projectId}/shift-reports/archived`}>
            {messages('shiftReports.archived')}
          </Tabs.Tab>
          <FeatureLimits featureName="shiftReportsManagerView">
            {() => (
              <Tabs.Tab
                onSelect={() => navigate(`/projects/${projectId}/shift-reports/manager`)}
                selected={location.pathname.endsWith('/manager')}
              >
                {messages('manager.managerView')}
              </Tabs.Tab>
            )}
          </FeatureLimits>
        </Tabs>
      </div>
      {isOnActivitiesTab && <ActivitiesToolbar projectId={projectId} />}
      <Page.Body>
        <Outlet />
      </Page.Body>
    </Page>
  );
};

export { ProjectShiftManager as Component };
