import { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueDetailsBasicSchema } from '@shape-construction/api/src/types';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { ProgressBarRoot } from '@shape-construction/arch-ui/src/ProgressBar/ProgressBar';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { getLabelByQualityPercentage } from 'app/pages/projects/[projectId]/data-book/utils/quality-labels';

type IssueQualityIndicatorProps = {
  qualityScore: IssueDetailsBasicSchema['qualityScore'];
};

const qualityLabelProgressColorMap = {
  notUseful: 'danger',
  theBasics: 'warning',
};

export const IssueQualityIndicator: React.FC<IssueQualityIndicatorProps> = ({ qualityScore }) => {
  const [issueQualityPopoverOpen, setIssueQualityPopoverOpen] = useState(false);
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const messages = useMessageGetter('issue.list.qualityIndicators');

  const category = getLabelByQualityPercentage(qualityScore ?? 0);

  const qualityData = useMemo(() => {
    return {
      title: messages(`${category}.title`),
      scoreRange: messages(`${category}.scoreRange`),
      description: messages(`${category}.description`),
      color: qualityLabelProgressColorMap[category]
    };
  }, [category, messages]);


  if (qualityScore === null || (category !== 'notUseful' && category !== 'theBasics')) {
    return null;
  }


  const handlePointerEnter = () => {
    if (isLargeScreen) {
      setIssueQualityPopoverOpen(true);
    }
  };

  const handlePointerLeave = () => {
    if (isLargeScreen) {
      setIssueQualityPopoverOpen(false);
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIssueQualityPopoverOpen(!issueQualityPopoverOpen);
  };

  return (
    <div
      className={'flex justify-center items-center'}
      onPointerEnter={handlePointerEnter}
      onPointerLeave={handlePointerLeave}
    >
      <Popover open={issueQualityPopoverOpen} onOpenChange={setIssueQualityPopoverOpen}>
        <Popover.Trigger onClick={handleClick} className="rounded-full w-auto p-0.5 hover:bg-neutral-subtle">
          <ProgressBarRoot progress={qualityScore} size="small" color={qualityData.color} variant="donut" />
        </Popover.Trigger>
        <Popover.Content side="bottom" align="center">
          <div id="quality-popover-content" className="flex flex-col gap-1 text-sm leading-6">
            <div className="font-semibold">{qualityData.title}</div>
            <div className="font-bold">{qualityData.scoreRange}</div>
            <div className="font-normal text-neutral">{qualityData.description}</div>
          </div>
        </Popover.Content>
      </Popover>
    </div>
  );
};
