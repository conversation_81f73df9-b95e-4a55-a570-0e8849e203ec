import React from 'react';
import { booleanFlagFactory } from '@shape-construction/api/factories/feature-flags';
import { issueFactory } from '@shape-construction/api/factories/issues';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/src/mock-handlers';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, userEvent } from 'tests/test-utils';
import { IssueCard } from './IssueCard';

describe('IssueCard', () => {
  describe('when clicking on the card', () => {
    it('appends the issueId query param by default', async () => {
      const issue = issueFactory({ id: 'issue-id' });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0?a=query'],
      });
      const route = { path: '/projects/:projectId' };
      render(<IssueCard issue={issue} />, {
        history,
        route,
      });

      await userEvent.click(screen.getByRole('link'));

      expect(history.location).toEqual(
        expect.objectContaining({
          pathname: '/projects/project-0',
          search: '?a=query&issueId=issue-id',
        })
      );
    });

    describe('and linkTo is passed', () => {
      it('visits the specified location', async () => {
        const issue = issueFactory({ id: 'issue-id' });
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0?a=query'],
        });
        const route = { path: '/projects/:projectId' };
        render(<IssueCard issue={issue} linkTo={{ pathname: '/go/somewhere/else' }} />, {
          history,
          route,
        });

        await userEvent.click(screen.getByRole('link'));

        expect(history.location).toEqual(
          expect.objectContaining({
            pathname: '/go/somewhere/else',
          })
        );
      });
    });
  });

  describe('when the issue has public updates', () => {
    it('highlights the item as having updates', () => {
      const issue = issueFactory({
        updates: {
          lastVisitedAt: new Date().toISOString(),
          unreadUpdatesCount: { public: 1, team: 0 },
        },
      });

      render(<IssueCard issue={issue} />, {});

      expect(screen.getByRole('status', { name: 'has updates' })).toBeInTheDocument();
    });
  });

  describe('when the issue has team updates', () => {
    it('highlights the item as having updates', () => {
      const issue = issueFactory({
        updates: {
          lastVisitedAt: new Date().toISOString(),
          unreadUpdatesCount: { public: 0, team: 1 },
        },
      });

      render(<IssueCard issue={issue} />, {});

      expect(screen.getByRole('status', { name: 'has updates' })).toBeInTheDocument();
    });
  });

  describe('when the issue does not have updates', () => {
    it('does not highlight the item as having updates', () => {
      const issue = issueFactory({
        updates: {
          lastVisitedAt: new Date().toISOString(),
          unreadUpdatesCount: { public: 0, team: 0 },
        },
      });

      render(<IssueCard issue={issue} />, {});

      expect(screen.queryByRole('status', { name: 'has updates' })).not.toBeInTheDocument();
    });
  });

  describe('when issue quality feature flags is enabled', () => {
    it('renders quality indicator', async () => {
      const issue = issueFactory({
        title: 'One issue',
        category: 'progress',
        referenceNumber: 'EXAMPLE-014',
        currentState: 'assignment_requested',
        critical: true,
        overdue: true,
        archived: true,
        activityLevel: 'idle',
        qualityScore: 18,
      });
      server.use(
        getApiFeatureFlagsMockHandler({
          user: [booleanFlagFactory('issue-quality-indicators', true)],
        })
      );

      render(<IssueCard issue={issue} />, {});

      expect(await screen.findByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('when issue quality feature flags is disabled', () => {
    it('does not render quality indicator', async () => {
      const issue = issueFactory({
        title: 'One issue',
        category: 'progress',
        referenceNumber: 'EXAMPLE-014',
        currentState: 'assignment_requested',
        critical: true,
        overdue: true,
        archived: true,
        activityLevel: 'idle',
        qualityScore: 18,
      });
      server.use(
        getApiFeatureFlagsMockHandler({
          user: [booleanFlagFactory('issue-quality-indicators', false)],
        })
      );

      render(<IssueCard issue={issue} />, {});

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
  });
});
