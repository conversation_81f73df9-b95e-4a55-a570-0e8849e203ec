import React, { createContext, useContext, useMemo, useState } from 'react';
import type { TeamMemberListSchema, UserBasicDetailsSchema } from '@shape-construction/api/src/types';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import { InputAdornment } from '@shape-construction/arch-ui/src/InputAdornment';
import type {
  SelectPanelSectionProps,
  SelectRootProps,
  SelectTriggerProps,
} from '@shape-construction/arch-ui/src/Select';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { renderChildren } from '@shape-construction/arch-ui/src/utils/render';
import { useConstructionRoles } from 'app/components/People/constructionRoles/hooks/useConstructionRoles';
import { PersonItem } from 'app/components/PersonItem/PersonItem';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectPeople } from 'app/queries/projects/people';
import { useCurrentUser } from 'app/queries/users/users';

type PersonSelectContextType = {
  value?: number;
  isLoadingPeople?: boolean;
  people?: TeamMemberListSchema;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
};

const PersonSelectContext = createContext<PersonSelectContextType>({
  isLoadingPeople: false,
  setSearchTerm: () => {},
});

const usePersonSelectContext = () => useContext(PersonSelectContext);

type PersonSelectRootProps = SelectRootProps<number, true>;
const PersonSelectRoot: React.FC<PersonSelectRootProps> = ({ value, children, ...props }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const project = useCurrentProject();
  const { data: people, isLoading: isLoadingPeople } = useProjectPeople(project.id, {
    team_id: project.currentTeamId,
    search: searchTerm,
  });

  const contextValue = useMemo(
    () => ({ isLoadingPeople, people, setSearchTerm, value }),
    [isLoadingPeople, people, setSearchTerm, value]
  );

  return (
    <PersonSelectContext.Provider value={contextValue}>
      <Select.Root {...props} value={value} onClose={() => setSearchTerm('')}>
        {children}
      </Select.Root>
    </PersonSelectContext.Provider>
  );
};

type PersonSelectTriggerProps = Omit<SelectTriggerProps<'button'>, 'children'> & {
  children?: ((user: UserBasicDetailsSchema | undefined) => React.ReactNode) | SelectTriggerProps<'button'>['children'];
};
const PersonSelectTrigger: React.FC<PersonSelectTriggerProps> = ({ children, ...props }) => {
  const { people, value } = usePersonSelectContext();
  const displayedUser: UserBasicDetailsSchema | undefined = useMemo(() => {
    return people?.find((person) => person.id === value)?.user;
  }, [people, value]);

  return (
    <Select.Trigger
      {...props}
      startAdornment={
        <InputAdornment>
          <UserAvatar user={displayedUser} size={props.size ?? 'md'} />
        </InputAdornment>
      }
    >
      {renderChildren(children, displayedUser)}
    </Select.Trigger>
  );
};

type PersonSelectPanelTitleProps = SelectPanelSectionProps & {
  label: string;
};
const PersonSelectPanelTitle: React.FC<PersonSelectPanelTitleProps> = ({ label, ...props }) => {
  return (
    <Select.PanelSection className="px-4 py-5 md:hidden text-base leading-6 font-medium" {...props}>
      <h1>{label}</h1>
    </Select.PanelSection>
  );
};

type PersonSelectSearchProps = {
  placeholder: string;
};
const PersonSelectSearch: React.FC<PersonSelectSearchProps> = ({ placeholder }) => {
  const { setSearchTerm } = usePersonSelectContext();

  const onSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (!value || value.length > 1) {
      setSearchTerm(value);
    }
  };

  return (
    <Select.PanelSection>
      <Select.Search placeholder={placeholder} onChange={onSearch} />
    </Select.PanelSection>
  );
};

type PersonalSelectOptionsProps = { currentPersonLabel: string; disableSelected?: boolean };
const PersonSelectOptions: React.FC<PersonalSelectOptionsProps> = ({ currentPersonLabel, disableSelected }) => {
  const { people, isLoadingPeople, value } = usePersonSelectContext();
  const currentUser = useCurrentUser();
  const { constructionRoles } = useConstructionRoles();

  if (isLoadingPeople) {
    return (
      <Select.Options>
        <Select.PanelSection className="flex-1 w-full flex flex-col justify-center">
          <SearchLoading />
        </Select.PanelSection>
      </Select.Options>
    );
  }

  if (people?.length === 0) {
    return (
      <Select.Options>
        <Select.PanelSection className="flex-1 w-full flex flex-col items-center justify-center">
          <NoSearchResults />
        </Select.PanelSection>
      </Select.Options>
    );
  }

  return (
    <Select.Options>
      {people?.map((person) => (
        <Select.Option
          key={person.id}
          value={person.id}
          className="md:min-h-14"
          disabled={disableSelected && value === person.id}
          aria-label={person.user.name}
        >
          <PersonItem
            avatar={<UserAvatar user={person.user} size="md" />}
            primaryLabel={person.user.name}
            secondaryLabel={person.team.displayName || ''}
            inlineLabel={person.user.id === currentUser?.id ? currentPersonLabel : undefined}
            inlineBadge={person.constructionRole ? constructionRoles[person.constructionRole].label : null}
          />
        </Select.Option>
      ))}
    </Select.Options>
  );
};

export const PersonSelect = {
  Root: PersonSelectRoot,
  Trigger: PersonSelectTrigger,
  Value: Select.Value,
  Panel: Select.ResponsivePanel,
  PanelTitle: PersonSelectPanelTitle,
  Search: PersonSelectSearch,
  Options: PersonSelectOptions,
};
