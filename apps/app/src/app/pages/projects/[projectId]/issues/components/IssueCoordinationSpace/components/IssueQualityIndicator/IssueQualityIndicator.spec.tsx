import { createIssueDetailsBasic } from '@shape-construction/api/src/factories/createIssueDetailsBasic';
import { getApiProjectsProjectIdIssuesIssueIdQualityIndicatorsMockHandler } from '@shape-construction/api/src/mock-handlers';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { IssueQualityIndicator } from './IssueQualityIndicator';

describe('<IssueQualityIndicator />', () => {
  describe('when qualityScore is not set', () => {
    it('does not render quality indicator button', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: undefined });

      render(<IssueQualityIndicator issue={issue} />);

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });
  });

  describe('when qualityScore is below 20%', () => {
    it('renders quality indicator button with `notUseful` label', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 19 });

      render(<IssueQualityIndicator issue={issue} />);

      expect(screen.getByRole('button', { name: 'issue.detail.header.quality.notUseful' })).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { name: 'Progress: 19%' })).toBeInTheDocument();
    });
  });

  describe('when qualityScore is between 20 and 39%', () => {
    it('renders quality indicator button with `theBasics` label', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 39 });

      render(<IssueQualityIndicator issue={issue} />);

      expect(screen.getByRole('button', { name: 'issue.detail.header.quality.theBasics' })).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { name: 'Progress: 39%' })).toBeInTheDocument();
    });
  });

  describe('when qualityScore is between 40 and 59%', () => {
    it('renders quality indicator button with `good` label', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 59 });

      render(<IssueQualityIndicator issue={issue} />);

      expect(screen.getByRole('button', { name: 'issue.detail.header.quality.good' })).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { name: 'Progress: 59%' })).toBeInTheDocument();
    });
  });

  describe('when qualityScore is between 60 and 79%', () => {
    it('renders quality indicator button with `veryGood` label', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 79 });

      render(<IssueQualityIndicator issue={issue} />);

      expect(screen.getByRole('button', { name: 'issue.detail.header.quality.veryGood' })).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { name: 'Progress: 79%' })).toBeInTheDocument();
    });
  });

  describe('when qualityScore is above 80%', () => {
    it('renders quality indicator button with `comprehensive` label', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 80 });

      render(<IssueQualityIndicator issue={issue} />);

      expect(screen.getByRole('button', { name: 'issue.detail.header.quality.comprehensive' })).toBeInTheDocument();
      expect(screen.getByRole('progressbar', { name: 'Progress: 80%' })).toBeInTheDocument();
    });
  });

  describe('when user hovers over quality indicator button', () => {
    it('shows tooltip', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 0 });
      const { user } = render(<IssueQualityIndicator issue={issue} />);

      await user.hover(screen.getByRole('button', { name: 'issue.detail.header.quality.notUseful' }));

      expect(await screen.findByRole('tooltip', { name: 'issue.detail.header.quality.tooltip' })).toBeInTheDocument();
    });
  });

  describe('when user clicks quality indicator button', () => {
    it('shows popover with quality details', async () => {
      const issue = createIssueDetailsBasic({ qualityScore: 0 });
      server.use(getApiProjectsProjectIdIssuesIssueIdQualityIndicatorsMockHandler());
      const { user } = render(<IssueQualityIndicator issue={issue} />);

      await user.click(screen.getByRole('button', { name: 'issue.detail.header.quality.notUseful' }));

      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });
});
