import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueDetailsBasicSchema } from '@shape-construction/api/src/types';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import {
  getLabelByQualityPercentage,
  type qualityLabels,
} from 'app/pages/projects/[projectId]/data-book/utils/quality-labels';
import { IssueQualityDetailsPopover } from './IssueQualityDetailsPopover';
import { IssueQualityIndicatorTooltip } from './IssueQualityIndicatorTooltip';

type IssueQualityIndicator = {
  issue: IssueDetailsBasicSchema;
};

export const IssueQualityIndicator: React.FC<IssueQualityIndicator> = ({ issue }) => {
  const qualityScore = issue.qualityScore ?? 0;
  const qualityLabel = useQualityLabel(qualityScore);

  if (!Number.isFinite(issue.qualityScore)) return;
  return (
    <IssueQualityDetailsPopover asChild issue={issue}>
      <IssueQualityIndicatorTooltip asChild>
        <button
          type="button"
          className={cn(
            'text-sm px-2 rounded hover:bg-neutral-subtlest-hovered flex gap-2 items-center',
            qualityLabel.className
          )}
        >
          <ProgressBar.Root progress={qualityScore} size="small" color={qualityLabel.progressColor} variant="donut" />
          {qualityLabel.text}
        </button>
      </IssueQualityIndicatorTooltip>
    </IssueQualityDetailsPopover>
  );
};

const qualityLabelClasses = {
  notUseful: { className: 'text-danger', progressColor: 'danger' },
  theBasics: { className: 'text-warning', progressColor: 'warning' },
  good: { className: 'text-brand', progressColor: 'primary' },
  veryGood: { className: 'text-success', progressColor: 'success' },
  comprehensive: { className: 'text-success', progressColor: 'success' },
} as const satisfies Record<qualityLabels, any>;

const useQualityLabel = (qualityScore: number) => {
  const messages = useMessageGetter('issue.detail.header.quality');
  // TODO: Move quality indicator utils out of data book
  const label = getLabelByQualityPercentage(qualityScore)!;
  return {
    text: messages(label),
    className: qualityLabelClasses[label].className,
    progressColor: qualityLabelClasses[label].progressColor,
  };
};
