import { postApiDirectUploadsType } from '@shape-construction/api/src/api';
import type { DirectUploadTypeSchema } from '@shape-construction/api/src/types';
import { Buffer } from 'buffer';
import * as FileSystem from 'expo-file-system';

type BlobAttributes = {
  filename: string;
  content_type: string;
  byte_size: number;
  checksum: string;
};

export const directUpload = async (fileUri: string, type: DirectUploadTypeSchema) => {
  const fileResponse = await fetch(fileUri);
  const blob = (await fileResponse.blob()) as Blob & { data: { name: string } };

  const contentType = blob.type;
  const fileName = blob.data.name;

  const meta = await FileSystem.getInfoAsync(fileUri, { md5: true, size: true });

  if (!meta.exists) throw new Error('File not found');

  const md5 = meta.md5 as string;

  const checksum = await Buffer.from(md5, 'hex').toString('base64');

  const blobAttributes: BlobAttributes = {
    filename: fileName!,
    content_type: contentType,
    byte_size: meta.size,
    checksum,
  };

  const signedImage = await postApiDirectUploadsType(type, { blob: blobAttributes });

  await fetch(signedImage.directUpload.url, {
    method: 'PUT',
    headers: signedImage.directUpload.headers as Record<string, string>,
    body: blob as unknown as Blob,
  });

  return signedImage;
};
