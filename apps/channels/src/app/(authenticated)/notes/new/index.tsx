import { ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Stack, useRouter } from 'expo-router';
import { t } from 'i18next';
import { Controller } from 'react-hook-form';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Label } from 'src/components/Form/Label';
import * as NotesEvidence from 'src/components/Notes/Form/Fields/NotesEvidenceField';
import { NotesObservationInput } from 'src/components/Notes/Form/Fields/NotesObservationInput';
import { NotesProjectSelector } from 'src/components/Notes/Form/Fields/NotesProjectSelector';
import { NotesTypeSelector } from 'src/components/Notes/Form/Fields/NotesTypeSelector';
import { useNotesFormContext, useNotesFormSubmit } from 'src/components/Notes/Form/NotesForm';

export default function NewNotePage() {
  const router = useRouter();
  const form = useNotesFormContext();
  const onSubmit = useNotesFormSubmit();

  return (
    <SafeAreaView edges={['bottom']} className="flex-1 justify-center items-center bg-neutral-subtlest">
      <View className="w-full flex-1">
        <Stack.Screen
          options={{
            headerShown: true,
            headerTitleAlign: 'center',
            title: t('notes.newNote.title'),
            headerLeft: () => (
              <TouchableOpacity accessibilityRole="button" onPress={router.back}>
                <ChevronLeftIcon className="text-black -left-2" />
              </TouchableOpacity>
            ),
          }}
        />
        <View className="p-4 flex-1 flex flex-col gap-4">
          <ScrollView showsVerticalScrollIndicator={false} className="flex-1 flex flex-col gap-4">
            <View className="flex-1 flex flex-col gap-4">
              {/* Project selector */}
              <Controller
                name="projectId"
                control={form.control}
                render={({ field }) => (
                  <NotesProjectSelector onValueChange={(option) => field.onChange(option?.value)} />
                )}
              />

              {/* Observation input */}
              <View className="flex flex-col gap-2">
                <Label required>{t('notes.newNote.form.observation.title')}</Label>
                <Controller
                  name="observation"
                  control={form.control}
                  render={({ field }) => <NotesObservationInput autoFocus {...field} onChangeText={field.onChange} />}
                />
              </View>

              {/* Notes type */}
              <View className="flex flex-col gap-2">
                <Label required>{t('notes.newNote.form.notesType.title')}</Label>
                <Controller
                  name="type"
                  control={form.control}
                  render={({ field }) => (
                    <NotesTypeSelector onValueChange={(option) => field.onChange(option?.value)} />
                  )}
                />
              </View>

              {/* Evidence field */}
              <View className="flex flex-col gap-2">
                <Text className="text-sm leading-5 font-bold text-neutral-subtle">
                  {t('notes.newNote.form.evidence.title')}
                </Text>
                <NotesEvidence.Field />
              </View>
              <View className="flex flex-row gap-2 justify-between self-center">
                <NotesEvidence.Preview className="h-40 w-[49%]" />
              </View>
            </View>
          </ScrollView>

          {/* Save button */}
          <TouchableOpacity
            accessibilityRole="button"
            disabled={!form.formState.isValid || form.formState.isSubmitting}
            className="py-3 px-4 rounded bg-brand-bold disabled:opacity-50"
            onPress={onSubmit}
          >
            <Text className="text-white text-center text-sm leading-5 font-medium">
              {t('notes.newNote.form.actions.saveNote')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
