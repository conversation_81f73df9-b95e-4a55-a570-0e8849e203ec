import { useEffect } from 'react';
import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { PortalHost as GorhomPortalHost } from '@gorhom/portal';
import { PortalHost as RNPrimitivesPortalHost } from '@rn-primitives/portal';
import { colors } from '@shape-construction/arch-ui-native/src/theme/colors-primitives';
import { useIsRestoring } from '@tanstack/react-query';
import { Redirect, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Image, Text, View } from 'react-native';
import { SessionProvider } from 'src/authentication/SessionProvider';
import { endSession } from 'src/authentication/session';
import { getToken } from 'src/authentication/tokens';
import { registerBackgroundTokenRefresh } from 'src/background_task/refresh_token_task';
import { AppNotificationEnablePromptBottomSheet } from 'src/components/AppNotificationEnablePromptBottomSheet/AppNotificationEnablePromptBottomSheet';
import { Chat } from 'src/components/Chat';
import { PushNotificationPermission } from 'src/components/PushNotificationPermission';
import { SyncPausedMutationsMutationDefaults } from 'src/components/Sync/SyncPausedMutations';
import { ChatClientProvider } from 'src/get-stream/ChatClientProvider';
import { useUsersMe } from 'src/get-stream/queries/users';

export const AuthenticatedLayout = () => {
  const { data: user, isLoading } = useUsersMe();
  const isRestoring = useIsRestoring();
  const isAuthenticated = !!user && getToken();

  useEffect(() => {
    if (user?.id) registerBackgroundTokenRefresh();
  }, [user?.id]);

  if (isLoading || isRestoring) return <PageLoader />;
  if (!isAuthenticated) {
    // Something is wrong!!!
    // The session token was removed but the user is still on cache. Force reset!
    endSession();
    return <Redirect href="/welcome" />;
  }

  return (
    <SessionProvider user={user}>
      <SyncPausedMutationsMutationDefaults />

      <PushNotificationPermission.Provider>
        <ActionSheetProvider>
          <ChatClientProvider>
            <Chat>
              <Stack screenOptions={{ contentStyle: { backgroundColor: colors['gray-50'] }, headerShown: false }} />
              <GorhomPortalHost name="appPortal" />
              <RNPrimitivesPortalHost />
            </Chat>
          </ChatClientProvider>
        </ActionSheetProvider>
        <PushNotificationPermission.PromptBottomSheet />
        <AppNotificationEnablePromptBottomSheet />
      </PushNotificationPermission.Provider>
    </SessionProvider>
  );
};

const PageLoader = () => {
  const { t } = useTranslation();
  return (
    <View className="relative w-full h-full flex flex-1 bg-white justify-center items-center">
      <StatusBar hidden />
      <Image
        source={require('../../../assets/splashscreen/splashscreen-light.png')}
        className="w-[100px] h-[100px]"
        resizeMode="contain"
      />
      <View className="absolute bottom-1/4 flex flex-row w-full items-center justify-center">
        <ActivityIndicator size="small" className="text-brand" />
        <Text className="font-semibold text-brand ml-3">{t('auth.loading')}</Text>
      </View>
    </View>
  );
};

export default AuthenticatedLayout;
