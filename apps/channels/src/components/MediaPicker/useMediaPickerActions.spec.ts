import { Toast } from '@shape-construction/arch-ui-native';
import { renderHook } from '@testing-library/react-native';
import { type DocumentPickerAsset, getDocumentAsync } from 'expo-document-picker';
import {
  type ImagePickerAsset,
  type ImagePickerSuccessResult,
  launchCameraAsync,
  launchImageLibraryAsync,
  requestCameraPermissionsAsync,
  requestMediaLibraryPermissionsAsync,
} from 'expo-image-picker';
import { requestForegroundPermissionsAsync } from 'expo-location';
import { useMediaPickerActions } from './useMediaPickerActions';

jest.mock('expo-image-picker');
jest.mock('expo-location');
jest.mock('expo-document-picker');

type GetDocumentAsyncReturn = ReturnType<typeof getDocumentAsync>;
const mockGetDocumentAsync = (args: Partial<Awaited<GetDocumentAsyncReturn>>) => {
  jest.mocked(getDocumentAsync).mockImplementation(() => Promise.resolve(args) as GetDocumentAsyncReturn);
};

type RequestForegroundPermissionsAsyncReturn = ReturnType<typeof requestForegroundPermissionsAsync>;
const mockRequestForegroundPermissionsAsync = (args: Partial<Awaited<RequestForegroundPermissionsAsyncReturn>>) => {
  jest
    .mocked(requestForegroundPermissionsAsync)
    .mockImplementation(() => Promise.resolve(args) as RequestForegroundPermissionsAsyncReturn);
};

type RequestCameraPermissionsAsyncReturn = ReturnType<typeof requestCameraPermissionsAsync>;
const mockRequestCameraPermissionsAsync = (args: Partial<Awaited<RequestCameraPermissionsAsyncReturn>>) => {
  jest
    .mocked(requestCameraPermissionsAsync)
    .mockImplementation(() => Promise.resolve(args) as RequestCameraPermissionsAsyncReturn);
};

type LaunchImageLibraryAsyncReturn = ReturnType<typeof launchImageLibraryAsync>;
const mockLaunchImageLibraryAsync = (args: Partial<Awaited<LaunchImageLibraryAsyncReturn>>) => {
  jest.mocked(launchImageLibraryAsync).mockImplementation(() => Promise.resolve(args) as LaunchImageLibraryAsyncReturn);
};

type RequestMediaLibraryPermissionsAsyncReturn = ReturnType<typeof requestMediaLibraryPermissionsAsync>;
const mockRequestMediaLibraryPermissionsAsync = (args: Partial<Awaited<RequestMediaLibraryPermissionsAsyncReturn>>) => {
  jest
    .mocked(requestMediaLibraryPermissionsAsync)
    .mockImplementation(() => Promise.resolve(args) as RequestMediaLibraryPermissionsAsyncReturn);
};

type LaunchCameraAsyncReturn = ReturnType<typeof launchCameraAsync>;
const mockLaunchCameraAsync = (args: Partial<Awaited<LaunchCameraAsyncReturn>>) => {
  jest.mocked(launchCameraAsync).mockImplementation(() => Promise.resolve(args) as LaunchCameraAsyncReturn);
};

describe('useMediaPicker', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('returns the attachment picker actions', () => {
    const { result } = renderHook(() => useMediaPickerActions());

    expect(result.current.camera).toBeDefined();
    expect(result.current.documents).toBeDefined();
    expect(result.current.gallery).toBeDefined();
  });

  describe('camera', () => {
    describe('when user does not allow camera permissions', () => {
      it('shows a toast message', async () => {
        const toastMock = jest.spyOn(Toast, 'show');
        mockRequestCameraPermissionsAsync({ granted: false });

        const { result } = renderHook(() => useMediaPickerActions());
        await result.current.camera();

        expect(toastMock).toHaveBeenCalledWith('permissions.camera.denied');
      });
    });

    describe('when user does allow camera permissions', () => {
      describe('and does not cancel the camera', () => {
        it('takes a photo and uploads it', async () => {
          const images: ImagePickerAsset[] = [
            {
              type: 'image',
              fileName: 'image.jpeg',
              fileSize: 100,
              assetId: 'file-3',
              height: 200,
              width: 200,
              uri: 'uri',
            },
          ];
          mockRequestCameraPermissionsAsync({ granted: true });
          mockLaunchCameraAsync({ canceled: false, assets: images });
          mockRequestForegroundPermissionsAsync({ granted: true });

          const { result } = renderHook(() => useMediaPickerActions());
          const cameraResult = (await result.current.camera()) as ImagePickerSuccessResult;

          expect(cameraResult.canceled).toBe(false);
          expect(cameraResult.assets).toEqual(images);
        });
      });
    });
  });

  describe('gallery', () => {
    describe('when user does not allow media library permissions', () => {
      it('shows a toast message', async () => {
        const toastMock = jest.spyOn(Toast, 'show');
        mockRequestMediaLibraryPermissionsAsync({ granted: false });

        const { result } = renderHook(() => useMediaPickerActions());
        await result.current.gallery();

        expect(toastMock).toHaveBeenCalledWith('permissions.mediaLibrary.denied');
      });
    });

    describe('when user allows media library permissions', () => {
      describe('and does not cancel the media picker', () => {
        it('uploads the images and videos', async () => {
          const videos: ImagePickerAsset[] = [
            {
              type: 'video',
              fileName: 'video1.mp4',
              duration: 10,
              assetId: 'file-1',
              height: 0,
              width: 0,
              uri: 'uri',
            },
            {
              type: 'video',
              fileName: 'video2.mp4',
              duration: 5,
              assetId: 'file-2',
              height: 0,
              width: 0,
              uri: 'uri',
            },
          ];
          const images: ImagePickerAsset[] = [
            {
              type: 'image',
              fileName: 'image.jpeg',
              assetId: 'file-3',
              height: 0,
              width: 0,
              uri: 'uri',
            },
          ];
          mockRequestMediaLibraryPermissionsAsync({ granted: true });
          mockLaunchImageLibraryAsync({ assets: [...videos, ...images] });
          const { result } = renderHook(() => useMediaPickerActions());

          const galleryResult = (await result.current.gallery()) as ImagePickerSuccessResult;

          expect(galleryResult.assets).toEqual([...videos, ...images]);
        });
      });
    });
  });

  describe('documents', () => {
    describe('when user does not cancel the documents picker', () => {
      it('uploads the documents', async () => {
        const assets: DocumentPickerAsset[] = [
          { name: 'document1.pdf', uri: 'uri1' },
          { name: 'document2.pdf', uri: 'uri2' },
        ];
        mockGetDocumentAsync({ assets, canceled: false });

        const { result } = renderHook(() => useMediaPickerActions());
        const documentsResult = await result.current.documents();

        expect(documentsResult.assets).toEqual(assets);
      });
    });
  });
});
