import { Toast } from '@shape-construction/arch-ui-native';
import { type DocumentPickerOptions, getDocumentAsync } from 'expo-document-picker';
import {
  type ImagePickerOptions,
  type ImagePickerResult,
  launchCameraAsync,
  launchImageLibraryAsync,
  requestCameraPermissionsAsync,
  requestMediaLibraryPermissionsAsync,
} from 'expo-image-picker';
import { requestForegroundPermissionsAsync } from 'expo-location';
import { useTranslation } from 'react-i18next';

type CancelableAction<T> = Promise<(T & { canceled: false }) | { canceled: true }>;

export const useMediaPickerActions = () => {
  const { t } = useTranslation();

  const camera = async (options?: ImagePickerOptions): CancelableAction<ImagePickerResult> => {
    const permissionCameraResult = await requestCameraPermissionsAsync();
    if (!permissionCameraResult.granted) {
      Toast.show(t('permissions.camera.denied'));
      return { canceled: true };
    }

    const locationPermissionResult = await requestForegroundPermissionsAsync();
    if (!locationPermissionResult.granted) Toast.show(t('permissions.location.denied'));

    return launchCameraAsync({
      allowsMultipleSelection: true,
      mediaTypes: ['images'],
      exif: true,
      orderedSelection: true,
      ...options,
    });
  };

  const gallery = async (options?: ImagePickerOptions): CancelableAction<ImagePickerResult> => {
    const permissionMediaLibraryResult = await requestMediaLibraryPermissionsAsync();
    if (!permissionMediaLibraryResult.granted) {
      Toast.show(t('permissions.mediaLibrary.denied'));
      return { canceled: true };
    }

    return launchImageLibraryAsync({
      allowsMultipleSelection: true,
      mediaTypes: ['images', 'videos'],
      exif: true,
      orderedSelection: true,
      ...options,
    });
  };

  const documents = async (options?: DocumentPickerOptions) => {
    return getDocumentAsync({ multiple: true, ...options });
  };

  return { camera, gallery, documents };
};
