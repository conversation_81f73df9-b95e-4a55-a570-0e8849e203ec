import { useEffect } from 'react';
import { useIsRestoring } from '@tanstack/react-query';
import { useSession } from 'src/authentication/SessionProvider';
import { queryClient } from 'src/react-query/query-client';
import { useCreateIssueMutationOptions } from '../Notes/Form/useCreateIssue';

export const SyncPausedMutationsMutationDefaults = () => {
  const { user } = useSession();
  const isAuthenticated = !!user;
  const isRestoring = useIsRestoring();
  const createIssueMutationOptions = useCreateIssueMutationOptions();

  useEffect(() => {
    queryClient.setMutationDefaults(createIssueMutationOptions.mutationKey!, createIssueMutationOptions);
  }, []);

  useEffect(() => {
    if (isAuthenticated || !isRestoring)
      queryClient.resumePausedMutations().then(() => {
        queryClient.invalidateQueries();
      });
  }, [isAuthenticated, isRestoring]);

  return null;
};
