import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ExpoDevice from 'expo-device';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { useSyncQueriesExternal } from 'react-query-external-sync';
import { queryClient } from 'src/react-query/query-client';

// https://github.com/LovesWorking/rn-better-dev-tools
export const Debugger = () => {
  // Set up the sync hook - automatically disabled in production!
  useSyncQueriesExternal({
    queryClient,
    socketURL: 'http://localhost:42831', // Default port for React Native DevTools
    deviceName: Platform?.OS ?? 'web', // Platform detection
    platform: Platform?.OS ?? 'web', // Use appropriate platform identifier
    deviceId: Platform?.OS ?? 'web', // Use a PERSISTENT identifier (see note below)
    isDevice: ExpoDevice.isDevice, // Automatically detects real devices vs emulators
    extraDeviceInfo: {
      // Optional additional info about your device
      appVersion: '1.0.0',
      // Add any relevant platform info
    },
    enableLogs: false,
    envVariables: {
      NODE_ENV: process.env.NODE_ENV,
      // Add any private environment variables you want to monitor
      // Public environment variables are automatically loaded
    },
    asyncStorage: AsyncStorage, // AsyncStorage for ['#storage', 'async', 'key'] queries + monitoring
    secureStorage: SecureStore, // SecureStore for ['#storage', 'secure', 'key'] queries + monitoring
    secureStorageKeys: ['authorization', 'authorization_refresh_key'], // SecureStore keys to monitor
  });

  return null;
};
