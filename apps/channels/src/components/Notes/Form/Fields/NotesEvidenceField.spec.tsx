import { FormProvider, useForm } from 'react-hook-form';
import { useMediaPickerActions } from 'src/components/MediaPicker/useMediaPickerActions';
import { render, renderHook, screen, userEvent, waitFor } from 'src/tests/test-utils';
import type { FormValues } from '../NotesForm';
import { Field, Preview } from './NotesEvidenceField';

const mockedUseMediaPickerActions = jest.mocked(useMediaPickerActions);
jest.mock('src/components/MediaPicker/useMediaPickerActions', () => ({
  useMediaPickerActions: jest.fn(() => ({
    camera: jest.fn(),
    gallery: jest.fn(),
    documents: jest.fn(),
  })),
}));

describe('<NotesEvidenceField.Field />', () => {
  const renderComponent = (defaultValues?: Partial<FormValues>) => {
    const { result: form } = renderHook(() => useForm<FormValues>({ defaultValues }));
    const result = render(
      <FormProvider {...form.current}>
        <Field />
      </FormProvider>
    );

    return { ...result, form };
  };

  it('renders camera and gallery buttons', () => {
    renderComponent();

    expect(screen.getByRole('button', { name: 'notes.newNote.form.evidence.camera' })).toBeOnTheScreen();
    expect(screen.getByRole('button', { name: 'notes.newNote.form.evidence.gallery' })).toBeOnTheScreen();
  });

  describe('when the camera button is pressed', () => {
    it('calls camera action', async () => {
      const spyOnCamera = jest.fn().mockResolvedValue({ canceled: false, assets: [] });
      mockedUseMediaPickerActions.mockReturnValue({ camera: spyOnCamera, documents: jest.fn(), gallery: jest.fn() });
      renderComponent();

      await userEvent.press(screen.getByRole('button', { name: 'notes.newNote.form.evidence.camera' }));

      expect(spyOnCamera).toHaveBeenCalled();
    });

    describe('when photo is taken', () => {
      it('updates the documents in form ', async () => {
        const spyOnCamera = jest.fn().mockResolvedValue({ canceled: false, assets: [{ uri: 'photo1.jpg' }] });
        mockedUseMediaPickerActions.mockReturnValue({ camera: spyOnCamera, documents: jest.fn(), gallery: jest.fn() });
        const { form } = renderComponent({ documents: [] });

        await userEvent.press(screen.getByRole('button', { name: 'notes.newNote.form.evidence.camera' }));

        await waitFor(() => {
          expect(form.current.getValues('documents')).toEqual(['photo1.jpg']);
        });
      });

      it('appends new documents to existing ones', async () => {
        const spyOnCamera = jest.fn().mockResolvedValue({ canceled: false, assets: [{ uri: 'photo2.jpg' }] });
        mockedUseMediaPickerActions.mockReturnValue({ camera: spyOnCamera, documents: jest.fn(), gallery: jest.fn() });
        const { form } = renderComponent({ documents: ['photo1.jpg'] });

        await userEvent.press(screen.getByRole('button', { name: 'notes.newNote.form.evidence.camera' }));

        await waitFor(() => {
          expect(form.current.getValues('documents')).toEqual(['photo1.jpg', 'photo2.jpg']);
        });
      });
    });
  });

  describe('when the gallery button is pressed', () => {
    it('calls gallery action', async () => {
      const spyOnGallery = jest.fn().mockResolvedValue({ canceled: false, assets: [] });
      mockedUseMediaPickerActions.mockReturnValue({ gallery: spyOnGallery, camera: jest.fn(), documents: jest.fn() });
      renderComponent();

      await userEvent.press(screen.getByRole('button', { name: 'notes.newNote.form.evidence.gallery' }));

      expect(spyOnGallery).toHaveBeenCalled();
    });

    describe('when media is selected', () => {
      it('updates the documents in form ', async () => {
        const spyOnGallery = jest.fn().mockResolvedValue({ canceled: false, assets: [{ uri: 'photo1.jpg' }] });
        mockedUseMediaPickerActions.mockReturnValue({ camera: jest.fn(), documents: jest.fn(), gallery: spyOnGallery });
        const { form } = renderComponent({ documents: [] });

        await userEvent.press(screen.getByRole('button', { name: 'notes.newNote.form.evidence.gallery' }));

        await waitFor(() => {
          expect(form.current.getValues('documents')).toEqual(['photo1.jpg']);
        });
      });

      it('appends new documents to existing ones', async () => {
        const spyOnGallery = jest.fn().mockResolvedValue({ canceled: false, assets: [{ uri: 'photo2.jpg' }] });
        mockedUseMediaPickerActions.mockReturnValue({ camera: jest.fn(), documents: jest.fn(), gallery: spyOnGallery });
        const { form } = renderComponent({ documents: ['photo1.jpg'] });

        await userEvent.press(screen.getByRole('button', { name: 'notes.newNote.form.evidence.gallery' }));

        await waitFor(() => {
          expect(form.current.getValues('documents')).toEqual(['photo1.jpg', 'photo2.jpg']);
        });
      });
    });
  });
});

describe('<NotesEvidenceField.Preview />', () => {
  const renderComponent = (defaultValues?: Partial<FormValues>) => {
    const { result: form } = renderHook(() => useForm<FormValues>({ defaultValues }));
    const result = render(
      <FormProvider {...form.current}>
        <Preview />
      </FormProvider>
    );

    return { ...result, form };
  };

  describe('when there is no evidence uploaded', () => {
    it('renders nothing', () => {
      renderComponent({ documents: [] });

      expect(screen.queryByRole('img')).not.toBeOnTheScreen();
    });
  });

  describe('when there is evidence uploaded', () => {
    it('displays the uploaded evidence', () => {
      renderComponent({ documents: ['photo1.jpg', 'photo2.jpg', 'photo3.jpg'] });

      expect(screen.getByRole('img', { name: 'photo1.jpg' })).toBeOnTheScreen();
      expect(screen.getByRole('img', { name: 'photo2.jpg' })).toBeOnTheScreen();
      expect(screen.getByRole('img', { name: 'photo3.jpg' })).toBeOnTheScreen();
    });
  });
});
