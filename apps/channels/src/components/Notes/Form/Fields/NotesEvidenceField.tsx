import type { ComponentProps } from 'react';
import { CameraPlusIcon, PhotoIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useTranslation } from 'react-i18next';
import { FlatList, Image, type ListRenderItem, Text, TouchableOpacity, View } from 'react-native';
import { useMediaPickerActions } from 'src/components/MediaPicker/useMediaPickerActions';
import { useNotesFormContext } from '../NotesForm';

const NotesEvidenceField = () => {
  const { t } = useTranslation();
  const { setValue, getValues } = useNotesFormContext();
  const { camera: cameraAction, gallery: galleryAction } = useMediaPickerActions();

  const onCamera = async () => {
    const result = await cameraAction();
    if (!result.canceled) {
      const documentsUri = result.assets.map(({ uri }) => uri);
      setValue('documents', [...(getValues('documents') ?? []), ...documentsUri]);
    }
  };

  const onGallery = async () => {
    const result = await galleryAction();
    if (!result.canceled) {
      const documentsUri = result.assets.map(({ uri }) => uri);
      setValue('documents', [...(getValues('documents') ?? []), ...documentsUri]);
    }
  };

  return (
    <View className="py-4 flex flex-row gap-2 border-dashed border-2 border-neutral-subtle rounded-md">
      <TouchableOpacity
        accessibilityRole="button"
        onPress={onCamera}
        className="flex-1 flex flex-col gap-2 items-center justify-center"
      >
        <CameraPlusIcon className="text-icon-neutral h-11 w-11" />
        <Text className="text-sm leading-5 font-medium text-link-brand">{t('notes.newNote.form.evidence.camera')}</Text>
      </TouchableOpacity>

      <View className="w-px border border-neutral-subtlest" />

      <TouchableOpacity
        accessibilityRole="button"
        onPress={onGallery}
        className="flex-1 flex flex-col gap-2 items-center justify-center"
      >
        <PhotoIcon className="text-icon-neutral h-11 w-11" />
        <Text className="text-sm leading-5 font-medium text-link-brand">
          {t('notes.newNote.form.evidence.gallery')}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const NotesEvidenceFieldPreview = (props: ComponentProps<typeof View>) => {
  const { watch } = useNotesFormContext();
  const documents = watch('documents');

  if (documents?.length === 0) return null;

  const renderImage: ListRenderItem<string> = ({ item: uri }) => (
    <Image accessibilityRole="image" alt={uri} source={{ uri }} className="h-40 flex-1 rounded-md object-cover" />
  );

  return (
    <FlatList
      data={documents}
      renderItem={renderImage}
      keyExtractor={(uri) => uri}
      numColumns={2}
      columnWrapperStyle={{ gap: 4 }}
      contentContainerStyle={{ gap: 4 }}
      scrollEnabled={false}
    />
  );
};

export { NotesEvidenceField as Field, NotesEvidenceFieldPreview as Preview };
