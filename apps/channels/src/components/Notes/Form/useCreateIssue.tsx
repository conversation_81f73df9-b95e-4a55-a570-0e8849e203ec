import { captureException } from '@sentry/react-native';
import type { ResponseErrorConfig } from '@shape-construction/api/client';
import {
  usePostApiProjectsProjectIdIssues,
  usePostApiProjectsProjectIdIssuesIssueIdDocuments,
} from '@shape-construction/api/src/hooks';
import type {
  AuthenticationErrorSchema,
  DirectUploadSchema,
  DocumentReferenceAndDocumentSchema,
  ErrorSchema,
  PostApiDirectUploadsType400Schema,
  PostApiDirectUploadsType401Schema,
  PostApiDirectUploadsType404Schema,
} from '@shape-construction/api/src/types';
import { Toast } from '@shape-construction/arch-ui-native';
import { type UseMutationOptions, useMutation } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { directUpload } from 'libs/direct-upload/activestorage';
import { useTranslation } from 'react-i18next';
import type { FormValues } from './NotesForm';

export const retryOnNetworkOrServerErrors = (error: AxiosError) => {
  return error?.response?.status === undefined || error.response.status >= 500;
};

const createIssueQueryKey = ['createIssue'];
export const useCreateIssueMutationOptions = (): UseMutationOptions<
  DocumentReferenceAndDocumentSchema[],
  ResponseErrorConfig<
    | ErrorSchema
    | AuthenticationErrorSchema
    | PostApiDirectUploadsType400Schema
    | PostApiDirectUploadsType401Schema
    | PostApiDirectUploadsType404Schema
  >,
  Pick<FormValues, 'projectId' | 'observation' | 'data' | 'documents'>
> => {
  const { t } = useTranslation();
  const createIssueMutation = usePostApiProjectsProjectIdIssues();
  const uploadIssueDocumentsMutation = usePostApiProjectsProjectIdIssuesIssueIdDocuments();
  const directUploadMutation = useMutation<
    DirectUploadSchema,
    ResponseErrorConfig<
      PostApiDirectUploadsType400Schema | PostApiDirectUploadsType401Schema | PostApiDirectUploadsType404Schema
    >,
    string
  >({
    mutationKey: ['directUpload'],
    mutationFn: (uri: string) => directUpload(uri, 'document'),
  });

  return {
    retry: (_, error) => retryOnNetworkOrServerErrors(error as AxiosError),
    mutationKey: createIssueQueryKey,
    mutationFn: async (data) => {
      // batch direct upload
      const documents = await Promise.all(
        data.documents?.map((uri) => {
          return directUploadMutation.mutateAsync(uri);
        }) || []
      );

      // create issue
      const issue = await createIssueMutation.mutateAsync({
        projectId: data.projectId,
        data: { issue: { title: data.observation } },
      });

      // batch upload issue documents
      return await Promise.all(
        documents?.map(({ signedId }) =>
          uploadIssueDocumentsMutation.mutateAsync({
            projectId: data.projectId,
            issueId: issue.id,
            data: { signed_id: signedId, caption: data.observation },
          })
        )
      );
    },
    onSuccess: () => {
      Toast.show(t('notes.newNote.form.feedback.success'));
    },
    onError: (e) => {
      const error = e.response?.data?.errorDescription ?? e.response?.data?.errorCode ?? e.message;
      Toast.show(t('notes.newNote.form.feedback.error', { error }));

      captureException(e);
    },
  };
};

export const useCreateIssue = () => {
  const createIssueMutationOptions = useCreateIssueMutationOptions();
  return useMutation(createIssueMutationOptions);
};
