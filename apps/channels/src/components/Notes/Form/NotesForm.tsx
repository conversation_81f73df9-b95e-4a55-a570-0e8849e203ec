import { yupResolver } from '@hookform/resolvers/yup';
import { captureException } from '@sentry/react-native';
import { Toast } from '@shape-construction/arch-ui-native';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import type { Resolver } from 'react-hook-form';
import { useForm, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { array, type InferType, lazy, mixed, object, string } from 'yup';
import { useCreateIssueMutationOptions } from './useCreateIssue';

const issueSchema = object({});
const schemaMap = { issue: issueSchema };

export const notesFormSchema = object({
  type: string()
    .oneOf(Object.keys(schemaMap) as (keyof typeof schemaMap)[])
    .required(),
  projectId: string().required(),
  observation: string().required(),
  documents: array(string()),
  data: mixed().when('type', (type) => {
    const typeKey = type as unknown as keyof typeof schemaMap;
    return type ? lazy(() => schemaMap[typeKey]) : mixed();
  }),
});

type BaseFormValues = {
  projectId: string;
  observation: string;
  documents?: string[];
};

export type FormValues = BaseFormValues & { type: 'issue'; data: InferType<typeof issueSchema> };

export const useNotesForm = (initialValues?: Partial<FormValues>) => {
  return useForm<FormValues>({
    defaultValues: initialValues,
    resolver: yupResolver(notesFormSchema) as unknown as Resolver<FormValues>,
  });
};

export const useNotesFormContext = () => useFormContext<FormValues>();

export const useNotesFormSubmit = () => {
  const { t } = useTranslation();
  const form = useNotesFormContext();
  const createIssueMutationOptions = useCreateIssueMutationOptions();
  const createIssueMutation = useMutation(createIssueMutationOptions);
  const router = useRouter();
  const onSubmit = form.handleSubmit(async (data) => {
    if (data.type === 'issue') {
      try {
        await createIssueMutation.mutateAsync(data, {
          onSuccess: () => {
            Toast.show(t('notes.newNote.form.feedback.success'));
            router.dismissTo('/');
          },
          onError: (e) => {
            Toast.show(t('notes.newNote.form.feedback.error', { error: e.message }));
          },
        });
      } catch (error) {
        captureException(error);
      }
    }
  });

  return onSubmit;
};
