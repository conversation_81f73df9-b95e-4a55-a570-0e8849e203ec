import type { ComponentProps } from 'react';
import { cn } from '@shape-construction/arch-ui-native';
import { Text, View } from 'react-native';

type LabelProps = ComponentProps<typeof Text> & {
  required?: boolean;
};

// To be moved to arch-ui-native as form component
export const Label = ({ children, className, required, ...props }: LabelProps) => {
  return (
    <View className="flex flex-row">
      <Text className={cn('text-sm leading-5 font-bold text-neutral-subtle', className)} {...props}>
        {children}
      </Text>
      {required && <Text className="text-sm leading-5 font-bold text-danger">*</Text>}
    </View>
  );
};
